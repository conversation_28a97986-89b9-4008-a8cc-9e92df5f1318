"use client";

import { memo } from "react";
import {
  Loader2,
  AlertCircle,
  FileText,
  Phone,
  MapPin,
  Truck,
  Shield,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { cn } from "@workspace/ui/lib/utils";
import type { Supplier, SupplierFormData, StatusEnum } from "../../types";

interface SupplierModalsProps {
  showAddModal: boolean;
  setShowAddModal: (show: boolean) => void;
  showEditModal: boolean;
  setShowEditModal: (show: boolean) => void;
  editingSupplier: Supplier | null;
  setEditingSupplier: (supplier: Supplier | null) => void;
  formData: SupplierFormData;
  setFormData: (data: SupplierFormData) => void;
  formLoading: boolean;
  formErrors: Partial<Record<keyof SupplierFormData, string>>;
  onCreateSupplier: (data: SupplierFormData) => Promise<void>;
  onUpdateSupplier: (id: string, data: SupplierFormData) => Promise<void>;
}

/**
 * Supplier Modals Component
 *
 * Handles both add and edit supplier modals.
 * Follows the cargo-management modal pattern for consistency.
 */
export const SupplierModals = memo<SupplierModalsProps>(
  ({
    showAddModal,
    setShowAddModal,
    showEditModal,
    setShowEditModal,
    editingSupplier,
    setEditingSupplier,
    formData,
    setFormData,
    formLoading,
    formErrors,
    onCreateSupplier,
    onUpdateSupplier,
  }) => {
    // Handle form input changes
    const handleInputChange = (
      field: keyof SupplierFormData,
      value: string
    ) => {
      setFormData({ ...formData, [field]: value });
    };

    // Handle form submission for creating supplier
    const handleCreateSubmit = async (event: React.FormEvent) => {
      event.preventDefault();
      await onCreateSupplier(formData);
    };

    // Handle form submission for updating supplier
    const handleUpdateSubmit = async (event: React.FormEvent) => {
      event.preventDefault();
      if (!editingSupplier) return;
      await onUpdateSupplier(editingSupplier.id, formData);
    };

    // Reset form and close modals
    const handleCloseAdd = () => {
      setShowAddModal(false);
    };

    const handleCloseEdit = () => {
      setShowEditModal(false);
      setEditingSupplier(null);
    };

    // InputField component matching cargo-management style
    const InputField = ({
      id,
      label,
      icon: Icon,
      error,
      ...props
    }: {
      id: string;
      label: string;
      icon?: React.ElementType;
      error?: string;
    } & React.InputHTMLAttributes<HTMLInputElement>) => (
      <div>
        <label
          htmlFor={id}
          className="block text-sm font-medium text-gray-700 mb-1.5"
        >
          {label}
        </label>
        <div className="relative">
          {Icon && (
            <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
          )}
          <Input
            id={id}
            className={cn(
              "w-full p-2.5 text-sm border rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors",
              Icon ? "pl-10" : "",
              error
                ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                : "border-gray-200"
            )}
            {...props}
          />
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
            <AlertCircle size={14} />
            {error}
          </p>
        )}
      </div>
    );

    return (
      <>
        {/* Add Supplier Modal */}
        <Dialog open={showAddModal} onOpenChange={handleCloseAdd}>
          <DialogContent className="sm:max-w-lg p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Truck className="h-5 w-5 text-primary" />
                Add Supplier
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateSubmit} className="p-4">
              <div className="space-y-3">
                <InputField
                  id="tracking_number"
                  label="Tracking Number *"
                  icon={FileText}
                  value={formData.tracking_number}
                  onChange={(e) =>
                    handleInputChange("tracking_number", e.target.value)
                  }
                  error={formErrors.tracking_number}
                  placeholder="Enter supplier tracking number"
                  required
                />
                <InputField
                  id="phone"
                  label="Phone Number"
                  icon={Phone}
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+****************"
                />
                <InputField
                  id="location"
                  label="Location"
                  icon={MapPin}
                  value={formData.location}
                  onChange={(e) =>
                    handleInputChange("location", e.target.value)
                  }
                  placeholder="City, Country"
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: StatusEnum) =>
                      handleInputChange("status", value)
                    }
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors border-gray-200">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Add Supplier"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Supplier Modal */}
        <Dialog open={showEditModal} onOpenChange={handleCloseEdit}>
          <DialogContent className="sm:max-w-lg p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Truck className="h-5 w-5 text-primary" />
                Edit Supplier
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleUpdateSubmit} className="p-4">
              <div className="space-y-3">
                <InputField
                  id="tracking_number"
                  label="Tracking Number *"
                  icon={FileText}
                  value={formData.tracking_number}
                  onChange={(e) =>
                    handleInputChange("tracking_number", e.target.value)
                  }
                  error={formErrors.tracking_number}
                  placeholder="Enter supplier tracking number"
                  required
                />
                <InputField
                  id="phone"
                  label="Phone Number"
                  icon={Phone}
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+****************"
                />
                <InputField
                  id="location"
                  label="Location"
                  icon={MapPin}
                  value={formData.location}
                  onChange={(e) =>
                    handleInputChange("location", e.target.value)
                  }
                  placeholder="City, Country"
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: StatusEnum) =>
                      handleInputChange("status", value)
                    }
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors border-gray-200">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Update"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </>
    );
  }
);

SupplierModals.displayName = "SupplierModals";
