"use client";

import { memo } from "react";
import {
  Loader2,
  AlertCircle,
  Users,
  Mail,
  Phone,
  MapPin,
  Shield,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { cn } from "@workspace/ui/lib/utils";
import type { Customer, CustomerFormData, StatusEnum } from "../../types";

interface CustomerModalsProps {
  // Modal states from hook
  showAddModal: boolean;
  setShowAddModal: (show: boolean) => void;
  showEditModal: boolean;
  setShowEditModal: (show: boolean) => void;
  editingCustomer: Customer | null;
  setEditingCustomer: (customer: Customer | null) => void;

  // Form data from hook
  formData: CustomerFormData;
  setFormData: (data: CustomerFormData) => void;
  formLoading: boolean;
  formErrors: Partial<Record<keyof CustomerFormData, string>>;

  // Functions from hook - supporting both prop names for compatibility
  handleCreateCustomer?: (data: CustomerFormData) => Promise<void>;
  handleUpdateCustomer?: (id: string, data: CustomerFormData) => Promise<void>;
  onCreateCustomer?: (data: CustomerFormData) => Promise<void>;
  onUpdateCustomer?: (id: string, data: CustomerFormData) => Promise<void>;
  resetCustomerForm?: () => void;
}

/**
 * Customer Modals Component
 *
 * Handles both add and edit customer modals.
 * Follows the cargo-management modal pattern for consistency.
 */
export const CustomerModals = memo<CustomerModalsProps>(
  ({
    showAddModal,
    setShowAddModal,
    showEditModal,
    setShowEditModal,
    editingCustomer,
    setEditingCustomer,
    formData,
    setFormData,
    formLoading,
    formErrors,
    handleCreateCustomer,
    handleUpdateCustomer,
    onCreateCustomer,
    onUpdateCustomer,
    resetCustomerForm,
  }) => {
    // Get the appropriate functions with fallback
    const createFn = handleCreateCustomer || onCreateCustomer;
    const updateFn = handleUpdateCustomer || onUpdateCustomer;

    return (
      <>
        {/* Add Customer Modal */}
        <Dialog
          open={showAddModal}
          onOpenChange={(open) => {
            setShowAddModal(open);
            if (!open && resetCustomerForm) {
              resetCustomerForm();
            }
          }}
        >
          <DialogContent className="sm:max-w-lg p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Add Customer
              </DialogTitle>
            </DialogHeader>
            <form
              onSubmit={async (e) => {
                e.preventDefault();
                if (createFn) {
                  await createFn(formData);
                }
              }}
              className="p-4"
            >
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Users className="h-4 w-4 text-gray-500" />
                    Customer Name *
                  </label>
                  <Input
                    placeholder="Enter customer name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.name ? "border-red-300" : "border-gray-200"
                    )}
                    required
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Customer Code
                  </label>
                  <Input
                    placeholder="Auto-generated"
                    value={formData.code}
                    onChange={(e) =>
                      setFormData({ ...formData, code: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-gray-50 text-gray-600 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                    disabled
                    title="Customer code will be auto-generated based on name and date"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Mail className="h-4 w-4 text-gray-500" />
                    Email Address
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) =>
                      setFormData({ ...formData, email: e.target.value })
                    }
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.email ? "border-red-300" : "border-gray-200"
                    )}
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.email}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Phone className="h-4 w-4 text-gray-500" />
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    Location
                  </label>
                  <Input
                    placeholder="City, Country"
                    value={formData.location}
                    onChange={(e) =>
                      setFormData({ ...formData, location: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      setFormData({ ...formData, status: value as StatusEnum })
                    }
                  >
                    <SelectTrigger className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2 mt-4 pt-3 border-t border-gray-100">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs border border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Create"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Customer Modal */}
        <Dialog
          open={showEditModal}
          onOpenChange={(open) => {
            setShowEditModal(open);
            if (!open) {
              setEditingCustomer(null);
              if (resetCustomerForm) {
                resetCustomerForm();
              }
            }
          }}
        >
          <DialogContent className="sm:max-w-sm p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Edit Customer
              </DialogTitle>
            </DialogHeader>
            <form
              onSubmit={async (e) => {
                e.preventDefault();
                if (!editingCustomer) return;
                if (updateFn) {
                  await updateFn(editingCustomer.id, formData);
                }
              }}
              className="p-4"
            >
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Users className="h-4 w-4 text-gray-500" />
                    Customer Name *
                  </label>
                  <Input
                    placeholder="Enter customer name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.name ? "border-red-300" : "border-gray-200"
                    )}
                    required
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Customer Code
                  </label>
                  <Input
                    placeholder="Auto-generated"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-gray-50 text-gray-600 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                    disabled
                    title="Customer code cannot be modified after creation"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Mail className="h-4 w-4 text-gray-500" />
                    Email Address
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.email ? "border-red-300" : "border-gray-200"
                    )}
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.email}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Phone className="h-4 w-4 text-gray-500" />
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    Location
                  </label>
                  <Input
                    placeholder="City, Country"
                    value={formData.location}
                    onChange={(e) =>
                      handleInputChange("location", e.target.value)
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      handleInputChange("status", value as StatusEnum)
                    }
                  >
                    <SelectTrigger className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2 mt-4 pt-3 border-t border-gray-100">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs border border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Update"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </>
    );
  }
);

CustomerModals.displayName = "CustomerModals";
