"use client";

import { memo } from "react";
import {
  RefreshCw,
  UserPlus,
  Building,
  UserCheck,
  Truck,
  Package,
  Calendar,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { useCustomerRelationsManagement } from "../hooks/useCustomerRelationsManagement";
import { OverviewTab } from "./tabs/OverviewTab";
import { CustomersTab } from "./tabs/CustomersTab";
import { SuppliersTab } from "./tabs/SuppliersTab";
import { CustomerModals } from "./modals/CustomerModals";
import { SupplierModals } from "./modals/SupplierModals";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";

/**
 * Customer Relations Container Component
 *
 * This is the main container component for customer relations management.
 * It follows the cargo-management pattern for consistency across the application.
 */
export const CustomerRelationsContainer = memo(() => {
  const {
    activeTab,
    setActiveTab,
    // State
    loading,
    refreshing,
    customers,
    suppliers,
    customerStats,
    supplierStats,

    // View modes
    customersViewMode,
    setCustomersViewMode,
    suppliersViewMode,
    setSuppliersViewMode,

    // Search and filters
    searchTerm,
    setSearchTerm,
    clientFilter,
    setClientFilter,
    supplierFilter,
    setSupplierFilter,
    supplierSearchTerm,
    setSupplierSearchTerm,

    // Actions
    handleRefresh,
    handleCreateCustomer,
    handleUpdateCustomer,
    handleDeleteCustomer,
    handleCreateSupplier,
    handleUpdateSupplier,
    handleDeleteSupplier,

    // Modal states
    showAddModal,
    setShowAddModal,
    showEditModal,
    setShowEditModal,
    editingCustomer,
    setEditingCustomer,
    showAddSupplierModal,
    setShowAddSupplierModal,
    showEditSupplierModal,
    setShowEditSupplierModal,
    editingSupplier,
    setEditingSupplier,

    // Form data
    formData,
    setFormData,
    formLoading,
    formErrors,
    supplierFormData,
    setSupplierFormData,
    supplierFormLoading,
    supplierFormErrors,
  } = useCustomerRelationsManagement();

  return (
    <Overview className="p-6 space-y-8">
      <Overview.Header
        title="Customer Relations"
        caption="Manage clients and maintain strong business relationships"
        actions={
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <ProtectedCreateButton entity="customers">
              <Button
                onClick={() => setShowAddModal(true)}
                className="flex items-center gap-2"
              >
                <UserPlus className="h-4 w-4" />
                Add Customer
              </Button>
            </ProtectedCreateButton>
            <ProtectedCreateButton entity="suppliers">
              <Button
                variant="outline"
                onClick={() => setShowAddSupplierModal(true)}
                className="flex items-center gap-2"
              >
                <UserPlus className="h-4 w-4" />
                Add Supplier
              </Button>
            </ProtectedCreateButton>
          </div>
        }
      />

      <Overview.Statistics>
        <Listing.StatCard
          icon={Building}
          name="Total Customers"
          value={customerStats.totalCustomers}
          valueType="number"
          caption={
            <span className="text-xs text-primary flex items-center gap-1">
              <RefreshCw className="h-3 w-3" /> Real-time data
            </span>
          }
          color="primary"
          loading={loading}
        />
        <Listing.StatCard
          icon={UserCheck}
          name="Active Customers"
          value={customerStats.activeCustomers}
          valueType="number"
          caption={
            <span className="text-xs text-green-600 flex items-center gap-1">
              <UserCheck className="h-3 w-3" /> Active clients
            </span>
          }
          color="green"
          loading={loading}
        />
        <Listing.StatCard
          icon={Truck}
          name="Total Suppliers"
          value={supplierStats.totalSuppliers}
          valueType="number"
          caption={
            <span className="text-xs text-blue-600 flex items-center gap-1">
              <Truck className="h-3 w-3" /> Active suppliers
            </span>
          }
          color="blue"
          loading={loading}
        />
        <Listing.StatCard
          icon={Package}
          name="Active Suppliers"
          value={supplierStats.activeSuppliers}
          valueType="number"
          caption={
            <span className="text-xs text-purple-600 flex items-center gap-1">
              <Package className="h-3 w-3" /> Active suppliers
            </span>
          }
          color="purple"
          loading={loading}
        />
        <Listing.StatCard
          icon={Calendar}
          name="New This Month"
          value={customerStats.newThisMonth + supplierStats.newThisMonth}
          valueType="number"
          caption={
            <span className="text-xs text-amber-600 flex items-center gap-1">
              <Calendar className="h-3 w-3" /> New clients and suppliers
            </span>
          }
          color="amber"
          loading={loading}
        />
      </Overview.Statistics>

      <Overview.Content>
        <Listing>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="customers">Customers</TabsTrigger>
              <TabsTrigger value="suppliers">Suppliers</TabsTrigger>
            </TabsList>
            <TabsContent value="overview">
              <OverviewTab
                customers={customers}
                customerStats={customerStats}
                suppliers={suppliers}
                supplierStats={supplierStats}
                loading={loading}
              />
            </TabsContent>
            <TabsContent value="customers">
              <CustomersTab
                customers={customers}
                loading={loading}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                clientFilter={clientFilter}
                setClientFilter={setClientFilter}
                viewMode={customersViewMode}
                onViewModeChange={setCustomersViewMode}
                onRefresh={handleRefresh}
                onAddCustomer={() => setShowAddModal(true)}
                onEditCustomer={(customer) => {
                  setEditingCustomer(customer);
                  setShowEditModal(true);
                }}
                onDeleteCustomer={handleDeleteCustomer}
              />
            </TabsContent>
            <TabsContent value="suppliers">
              <SuppliersTab
                suppliers={suppliers}
                loading={loading}
                searchTerm={supplierSearchTerm}
                setSearchTerm={setSupplierSearchTerm}
                supplierFilter={supplierFilter}
                setSupplierFilter={setSupplierFilter}
                viewMode={suppliersViewMode}
                onViewModeChange={setSuppliersViewMode}
                onRefresh={handleRefresh}
                onAddSupplier={() => setShowAddSupplierModal(true)}
                onEditSupplier={(supplier) => {
                  setEditingSupplier(supplier);
                  setShowEditSupplierModal(true);
                }}
                onDeleteSupplier={handleDeleteSupplier}
              />
            </TabsContent>
          </Tabs>

          {/* Customer Modals */}
          <CustomerModals
            showAddModal={showAddModal}
            setShowAddModal={setShowAddModal}
            showEditModal={showEditModal}
            setShowEditModal={setShowEditModal}
            editingCustomer={editingCustomer}
            setEditingCustomer={setEditingCustomer}
            formData={formData}
            setFormData={setFormData}
            formLoading={formLoading}
            formErrors={formErrors}
            onCreateCustomer={handleCreateCustomer}
            onUpdateCustomer={handleUpdateCustomer}
          />

          {/* Supplier Modals */}
          <SupplierModals
            showAddModal={showAddSupplierModal}
            setShowAddModal={setShowAddSupplierModal}
            showEditModal={showEditSupplierModal}
            setShowEditModal={setShowEditSupplierModal}
            editingSupplier={editingSupplier}
            setEditingSupplier={setEditingSupplier}
            formData={supplierFormData}
            setFormData={setSupplierFormData}
            formLoading={supplierFormLoading}
            formErrors={supplierFormErrors}
            onCreateSupplier={handleCreateSupplier}
            onUpdateSupplier={handleUpdateSupplier}
          />
        </Listing>
      </Overview.Content>
    </Overview>
  );
});

CustomerRelationsContainer.displayName = "CustomerRelationsContainer";
