"use client";

import { useState, useEffect, useCallback } from "react";
import { useAppSelector } from "@/store/hooks";
import {
  customerService,
  supplierService,
  type CustomerInsert,
  type CustomerUpdate,
  type SupplierInsert,
  type SupplierUpdate,
} from "@/lib/logistics";
import type {
  Customer,
  Supplier,
  CustomerFormData,
  SupplierFormData,
  CustomerStats,
  SupplierStats,
} from "../types";

/**
 * Custom hook for customer relations management
 *
 * Handles all state management, data fetching, and CRUD operations
 * for customers and suppliers. Follows the cargo-management pattern.
 */
export const useCustomerRelationsManagement = () => {
  // Auth state
  const { user: authUser, session } = useAppSelector((state) => state.auth);

  // Tab state
  const [activeTab, setActiveTab] = useState("overview");

  // View mode states (default to table as per user preference)
  const [customersViewMode, setCustomersViewMode] = useState<"cards" | "table">(
    "table"
  );
  const [suppliersViewMode, setSuppliersViewMode] = useState<"cards" | "table">(
    "table"
  );

  // Loading states
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Data state
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [customerStats, setCustomerStats] = useState<CustomerStats>({
    total: 0,
    active: 0,
    newThisMonth: 0,
    byStatus: [],
  });
  const [supplierStats, setSupplierStats] = useState<SupplierStats>({
    total: 0,
    active: 0,
    newThisMonth: 0,
    byStatus: [],
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [clientFilter, setClientFilter] = useState("all");
  const [supplierFilter, setSupplierFilter] = useState("all");
  const [supplierSearchTerm, setSupplierSearchTerm] = useState("");

  // Customer modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [formData, setFormData] = useState<CustomerFormData>({
    name: "",
    code: "",
    email: "",
    phone: "",
    location: "",
    status: "ACTIVE",
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<
    Partial<Record<keyof CustomerFormData, string>>
  >({});

  // Supplier modal states
  const [showAddSupplierModal, setShowAddSupplierModal] = useState(false);
  const [showEditSupplierModal, setShowEditSupplierModal] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [supplierFormData, setSupplierFormData] = useState<SupplierFormData>({
    tracking_number: "",
    phone: "",
    location: "",
    status: "ACTIVE",
  });
  const [supplierFormLoading, setSupplierFormLoading] = useState(false);
  const [supplierFormErrors, setSupplierFormErrors] = useState<
    Partial<Record<keyof SupplierFormData, string>>
  >({});

  // Fetch customers
  const fetchCustomers = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setRefreshing(true);
        else setLoading(true);

        // Fetch customers with search if provided
        let result;
        if (searchTerm.trim()) {
          result = await customerService.searchCustomers(searchTerm, {
            column: "created_at",
            ascending: false,
            limit: 1000,
          });
        } else {
          result = await customerService.getActiveCustomers({
            column: "created_at",
            ascending: false,
            limit: 1000,
          });
        }

        if (result.success) {
          setCustomers(result.data);

          // Calculate statistics
          const data = result.data;
          const activeCount = data.filter((c) => c.status === "ACTIVE").length;
          const thisMonth = new Date();
          thisMonth.setDate(1);
          const newThisMonth = data.filter(
            (c) => c.created_at && new Date(c.created_at) >= thisMonth
          ).length;

          // Group by status
          const statusGroups = data.reduce((acc: any, customer) => {
            const status = customer.status || "UNKNOWN";
            if (!acc[status]) {
              acc[status] = 0;
            }
            acc[status]++;
            return acc;
          }, {});

          const customersByStatus = Object.entries(statusGroups).map(
            ([status, count]) => ({
              status,
              count: count as number,
            })
          );

          setCustomerStats({
            total: data.length,
            active: activeCount,
            newThisMonth,
            byStatus: customersByStatus,
          });
        }
      } catch (error) {
        console.error("Error fetching customers:", error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [authUser, searchTerm]
  );

  // Fetch suppliers
  const fetchSuppliers = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setRefreshing(true);
        else setLoading(true);

        // Check if supplier service is available
        if (!supplierService) {
          console.error("Supplier service not available");
          setSuppliers([]);
          return;
        }

        // Fetch suppliers with search if provided
        let result;
        if (supplierSearchTerm && supplierSearchTerm.trim()) {
          result = await supplierService.searchSuppliers(supplierSearchTerm, {
            column: "created_at",
            ascending: false,
            limit: 1000,
          });
        } else {
          result = await supplierService.getActiveSuppliers({
            column: "created_at",
            ascending: false,
            limit: 1000,
          });
        }

        if (result && result.success) {
          setSuppliers(result.data || []);

          // Calculate statistics
          const data = result.data;
          const activeCount = data.filter((s) => s.status === "ACTIVE").length;
          const thisMonth = new Date();
          thisMonth.setDate(1);
          const newThisMonth = data.filter(
            (s) => s.created_at && new Date(s.created_at) >= thisMonth
          ).length;

          // Group by status
          const statusGroups = data.reduce((acc: any, supplier) => {
            const status = supplier.status || "UNKNOWN";
            if (!acc[status]) {
              acc[status] = 0;
            }
            acc[status]++;
            return acc;
          }, {});

          const suppliersByStatus = Object.entries(statusGroups).map(
            ([status, count]) => ({
              status,
              count: count as number,
            })
          );

          setSupplierStats({
            total: data.length,
            active: activeCount,
            newThisMonth,
            byStatus: suppliersByStatus,
          });
        } else {
          console.error("Failed to fetch suppliers:", result?.error);
          setSuppliers([]);
        }
      } catch (error) {
        console.error("Error fetching suppliers:", error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [authUser, supplierSearchTerm]
  );

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    await Promise.all([fetchCustomers(true), fetchSuppliers(true)]);
  }, [fetchCustomers, fetchSuppliers]);

  // Customer CRUD operations
  const handleCreateCustomer = useCallback(
    async (data: CustomerFormData) => {
      if (!session?.account_id) return;

      setFormLoading(true);
      try {
        const customerData: CustomerInsert = {
          name: data.name.trim(),
          email: data.email.trim() || null,
          phone: data.phone.trim() || null,
          location: data.location.trim() || null,
          status: data.status,
          account_id: session.account_id,
        };

        const result = await customerService.createCustomer(customerData);

        if (result.success) {
          setShowAddModal(false);
          resetCustomerForm();
          await fetchCustomers(true);
        } else {
          setFormErrors({ name: result.error || "Failed to create customer" });
        }
      } catch (error: any) {
        setFormErrors({
          name: error.message || "An unexpected error occurred",
        });
      } finally {
        setFormLoading(false);
      }
    },
    [session?.account_id, fetchCustomers]
  );

  const handleUpdateCustomer = useCallback(
    async (id: string, data: CustomerFormData) => {
      setFormLoading(true);
      try {
        const updateData: CustomerUpdate = {
          name: data.name.trim(),
          email: data.email.trim() || null,
          phone: data.phone.trim() || null,
          location: data.location.trim() || null,
          status: data.status,
        };

        const result = await customerService.updateCustomer(id, updateData);

        if (result.success) {
          setShowEditModal(false);
          setEditingCustomer(null);
          resetCustomerForm();
          await fetchCustomers(true);
        } else {
          setFormErrors({ name: result.error || "Failed to update customer" });
        }
      } catch (error: any) {
        setFormErrors({
          name: error.message || "An unexpected error occurred",
        });
      } finally {
        setFormLoading(false);
      }
    },
    [fetchCustomers]
  );

  const handleDeleteCustomer = useCallback(
    async (customer: Customer) => {
      if (
        !confirm(
          `Are you sure you want to delete "${customer.name}"? This action cannot be undone.`
        )
      ) {
        return;
      }

      try {
        const result = await customerService.delete(customer.id);

        if (result.success) {
          await fetchCustomers(true);
        } else {
          alert(`Failed to delete customer: ${result.error}`);
        }
      } catch (error: any) {
        alert(`Failed to delete customer: ${error.message}`);
      }
    },
    [fetchCustomers]
  );

  // Supplier CRUD operations
  const handleCreateSupplier = useCallback(
    async (data: SupplierFormData) => {
      setSupplierFormLoading(true);
      setSupplierFormErrors({});

      try {
        const supplierData: SupplierInsert = {
          tracking_number: data.tracking_number,
          phone: data.phone,
          location: data.location,
          status: data.status as any, // Cast to handle status type compatibility
        };

        const result = await supplierService.createSupplier(supplierData);

        if (result.success) {
          setShowAddSupplierModal(false);
          resetSupplierForm();
          await fetchSuppliers(true);
        } else {
          setSupplierFormErrors({
            tracking_number: result.error || "Failed to create supplier",
          });
        }
      } catch (error: any) {
        setSupplierFormErrors({
          tracking_number: error.message || "An unexpected error occurred",
        });
      } finally {
        setSupplierFormLoading(false);
      }
    },
    [fetchSuppliers]
  );

  const handleUpdateSupplier = useCallback(
    async (id: string, data: SupplierFormData) => {
      setSupplierFormLoading(true);
      setSupplierFormErrors({});

      try {
        const supplierData: SupplierUpdate = {
          tracking_number: data.tracking_number,
          phone: data.phone,
          location: data.location,
          status: data.status as any, // Cast to handle status type compatibility
        };

        const result = await supplierService.updateSupplier(id, supplierData);

        if (result.success) {
          setShowEditSupplierModal(false);
          setEditingSupplier(null);
          resetSupplierForm();
          await fetchSuppliers(true);
        } else {
          setSupplierFormErrors({
            tracking_number: result.error || "Failed to update supplier",
          });
        }
      } catch (error: any) {
        setSupplierFormErrors({
          tracking_number: error.message || "An unexpected error occurred",
        });
      } finally {
        setSupplierFormLoading(false);
      }
    },
    [fetchSuppliers]
  );

  const handleDeleteSupplier = useCallback(
    async (supplier: Supplier) => {
      if (
        !confirm(
          `Are you sure you want to delete supplier "${supplier.tracking_number || "this supplier"}"? This action cannot be undone.`
        )
      ) {
        return;
      }

      try {
        const result = await supplierService.deleteSupplier(supplier.id);

        if (result.success) {
          await fetchSuppliers(true);
        } else {
          alert(`Failed to delete supplier: ${result.error}`);
        }
      } catch (error: any) {
        alert(`Failed to delete supplier: ${error.message}`);
      }
    },
    [fetchSuppliers]
  );

  // Initial fetch
  useEffect(() => {
    if (authUser) {
      fetchCustomers();
      fetchSuppliers();
    }
  }, [authUser, fetchCustomers, fetchSuppliers]);

  // Refetch customers when search term changes
  useEffect(() => {
    if (authUser && searchTerm !== undefined) {
      fetchCustomers();
    }
  }, [searchTerm, authUser, fetchCustomers]);

  // Refetch suppliers when supplier search term changes
  useEffect(() => {
    if (authUser && supplierSearchTerm !== undefined) {
      fetchSuppliers();
    }
  }, [supplierSearchTerm, authUser, fetchSuppliers]);

  // Populate form data when editing customer
  useEffect(() => {
    if (editingCustomer) {
      setFormData({
        name: editingCustomer.name,
        code: editingCustomer.code || "",
        email: editingCustomer.email || "",
        phone: editingCustomer.phone || "",
        location: editingCustomer.location || "",
        status: editingCustomer.status || "ACTIVE",
      });
    }
  }, [editingCustomer]);

  // Populate form data when editing supplier
  useEffect(() => {
    if (editingSupplier) {
      setSupplierFormData({
        tracking_number: editingSupplier.tracking_number || "",
        phone: editingSupplier.phone || "",
        location: editingSupplier.location || "",
        status: (editingSupplier.status || "ACTIVE") as "ACTIVE",
      });
    }
  }, [editingSupplier]);

  // Reset form functions
  const resetCustomerForm = () => {
    setFormData({
      name: "",
      code: "",
      email: "",
      phone: "",
      location: "",
      status: "ACTIVE",
    });
    setFormErrors({});
  };

  const resetSupplierForm = () => {
    setSupplierFormData({
      tracking_number: "",
      phone: "",
      location: "",
      status: "ACTIVE",
    });
    setSupplierFormErrors({});
  };

  return {
    // State
    activeTab,
    setActiveTab,
    loading,
    refreshing,
    customers,
    suppliers,
    customerStats,
    supplierStats,

    // View modes
    customersViewMode,
    setCustomersViewMode,
    suppliersViewMode,
    setSuppliersViewMode,

    // Search and filters
    searchTerm,
    setSearchTerm,
    clientFilter,
    setClientFilter,
    supplierFilter,
    setSupplierFilter,
    supplierSearchTerm,
    setSupplierSearchTerm,

    // Actions
    handleRefresh,
    handleCreateCustomer,
    handleUpdateCustomer,
    handleDeleteCustomer,
    handleCreateSupplier,
    handleUpdateSupplier,
    handleDeleteSupplier,
    resetCustomerForm,
    resetSupplierForm,

    // Modal states
    showAddModal,
    setShowAddModal,
    showEditModal,
    setShowEditModal,
    editingCustomer,
    setEditingCustomer,
    showAddSupplierModal,
    setShowAddSupplierModal,
    showEditSupplierModal,
    setShowEditSupplierModal,
    editingSupplier,
    setEditingSupplier,

    // Form data
    formData,
    setFormData,
    formLoading,
    formErrors,
    supplierFormData,
    setSupplierFormData,
    supplierFormLoading,
    supplierFormErrors,
  };
};
