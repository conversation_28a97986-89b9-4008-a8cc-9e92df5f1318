import type { Customer, Supplier, StatusEnum } from "@/lib/logistics";
import type { BaseEntityStats } from "@/lib/types/shared";

// Customer form data interface
export interface CustomerFormData {
  name: string;
  code: string;
  email: string;
  phone: string;
  location: string;
  status: StatusEnum;
}

// Supplier form data interface
export interface SupplierFormData {
  tracking_number: string;
  phone: string;
  location: string;
  status: StatusEnum;
}

// Customer statistics interface
export interface CustomerStats extends BaseEntityStats {
  // Using shared BaseEntityStats
}

// Supplier statistics interface
export interface SupplierStats extends BaseEntityStats {
  // Using shared BaseEntityStats
}

// Transform customer data for display
export const transformCustomerForDisplay = (customer: Customer) => ({
  id: customer.id,
  name: customer.name,
  code: customer.code || "Not assigned",
  email: customer.email || "No email",
  phone: customer.phone || "No phone",
  location: customer.location || "Unknown location",
  status: customer.status || "UNKNOWN",
  created_at: customer.created_at || "",
  account_id: customer.account_id || "",
});

// Transform supplier data for display
export const transformSupplierForDisplay = (supplier: Supplier) => ({
  id: supplier.id,
  phone: supplier.phone || "",
  location: supplier.location || "",
  tracking_number: supplier.tracking_number || "",
  status: supplier.status || "UNKNOWN",
  created_at: supplier.created_at || "",
});
