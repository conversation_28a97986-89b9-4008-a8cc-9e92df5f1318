"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, redirect } from "next/navigation";
import {
  AlertCircle,
  Package,
  PencilLine,
  Loader2,
  RefreshCw,
  ArrowLeft,
  Container,
  UserPlus,
  UserMinus,
  Info,
  Hash,
  Clock,
  Ruler,
  CheckCircle,
  CheckSquare,
  FileText,
  Weight,
  Box,
  Users,
  Truck,
  QrCode,
  MoreHorizontal,
  Edit,
  Trash2,
  DollarSign,
  Receipt,
  Landmark,
  ReceiptText,
  Plane,
  Ship,
  Skull,
  ShieldCheck as Shield,
  Plus,
  Download,
  SquareArrowOutUpRight as GotoIcon,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";

// Note: AlertDialog imports removed since confirmation dialogs are now handled
// by the centralized useCargoManagement hook
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { cn } from "@workspace/ui/lib/utils";
import { PageTransition } from "@/components/page-transition";

import { StatusBadge } from "@/components/status-badge";
import {
  batchService,
  cargoService,
  type BatchWithRelations,
  type CargoWithRelations,
  BatchTypeEnum as BatchType,
  DimensionUnitEnum as DimensionUnit,
} from "@/lib/logistics";
import {
  ProtectedCreateButton,
  ProtectedEditButton,
  withRBAC,
} from "@/lib/components/RBACWrapper";
import { EditBatchDialog } from "../forms/EditBatchDialog";
import {
  CBMCapacityAlert,
  CBMExceededDialog,
  CBMSummaryCard,
  WeightCapacityAlert,
  WeightSummaryCard,
} from "@/components/batch-management/cbm-alerts";
import { EditCargoDialog } from "../../cargo-management/components/Dialog/EditCargoDialog";
import { QRCodeDialog } from "../../cargo-management/components/Dialog/QRCodeDialog";
import { CargoDetailsDialog } from "../../cargo-management/components/Dialog/CargoDetailsDialog";
import { type CargoDisplay as CargoManagementCargoDisplay } from "../../cargo-management/utils/types";
import { NewCargoForm } from "../../cargo-management/forms/NewCargoForm";
import { CustomerModals } from "../../customer-relations/components/modals/CustomerModals";
import { SupplierModals } from "../../customer-relations/components/modals/SupplierModals";
import { useCustomerRelationsManagement } from "../../customer-relations/hooks/useCustomerRelationsManagement";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
// Table components removed as we now use Listing.Table

import {
  calculateTotalCBM,
  calculateCBMUtilization,
  getDimensionUnitAbbreviation,
  calculateTotalWeight,
} from "@/lib/utils/unit-mappings";
import {
  FilterPanel,
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import { useAuth } from "@/components/providers/AuthProvider";
import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { toast } from "sonner";
import {
  BulkStatusSelector,
  CARGO_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { useCargoManagement } from "@/app/(app)/cargo-management/hooks/useCargoManagement";
import { ExportDialog } from "@/components/ui/export-dialog";
import { useCargoExportDialog } from "@/components/ui/export-dialog/useExportDialog";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

// Types for batch activity history
interface BatchActivityItem {
  activity: string;
  date: string;
  time: string;
  location: string;
  completed: boolean;
  icon: React.ElementType;
  description?: string;
}

// Types for customer display
interface CustomerDisplay {
  id: string;
  name: string;
  email: string;
  cargoCount: number;
  totalWeight: number;
  totalCBM: number;
}

// Types for cargo display (matching cargo-management structure)
interface CargoDisplay {
  id: string;
  trackingNumber: string;
  chinaTrackingNumber?: string;
  type: string;
  origin: string;
  destination: string;
  status: string;
  weight: string;
  cbm: string;
  customer: string;
  customerId: string;
  customerPhone?: string;
  entityType: "customer" | "supplier" | "unknown";
  date: string;
  updatedAt: string;
  particular?: string;
  batchCode?: string;
  batchId?: string;
  // New fields for enhanced display
  ctn?: number;
  dimensions?: string;
  unitPrice?: string;
  totalPrice?: string;
  invoiceStatus?: string;
  assignedTo?: string;
  assignedToId?: string;
}

// Helper function to get batch type display
const getBatchTypeDisplay = (
  type: BatchType | string | null
): { type: string; icon: React.ElementType } => {
  switch (type?.toLowerCase()) {
    case "container":
      return { type: "Container", icon: Container };
    case "pallet":
      return { type: "Pallet", icon: Box };
    case "bulk":
      return { type: "Bulk", icon: Package };
    case "mixed":
      return { type: "Mixed", icon: Package };
    default:
      return { type: "General", icon: Package };
  }
};

// Helper function to format dimensions
const formatDimensions = (
  length: number | null,
  width: number | null,
  height: number | null,
  unit: DimensionUnit | string | null
): string => {
  if (!length || !width || !height) return "N/A";
  const unitDisplay =
    unit === "METER_CUBIC" ? "m" : unit === "FEET_CUBIC" ? "ft" : "cm";
  return `${length} × ${width} × ${height} ${unitDisplay}`;
};

// Helper function to format CBM
const formatCBM = (
  cbm: number | null,
  unit: DimensionUnit | string | null
): string => {
  if (!cbm) return "N/A";
  const unitDisplay =
    unit === "METER_CUBIC"
      ? "m³"
      : unit === "FEET_CUBIC"
        ? "ft³"
        : unit === "CENTIMETER_CUBIC"
          ? "cm³"
          : unit === "INCH_CUBIC"
            ? "in³"
            : "m³";
  return `${cbm.toLocaleString()} ${unitDisplay}`;
};

// Helper function to format weight
const formatWeight = (weight: number | null): string => {
  if (!weight) return "N/A";
  return `${weight.toLocaleString()} kg`;
};

// Helper function to format batch code
const formatBatchCode = (code: string | null): string => {
  if (!code) return "N/A";
  return code.toUpperCase();
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper function to format datetime
const formatDateTime = (
  dateString: string | null
): { date: string; time: string } => {
  if (!dateString) return { date: "N/A", time: "N/A" };
  const date = new Date(dateString);
  return {
    date: date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }),
    time: date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  };
};

// Cargo Card Renderer Component for Batch Page
const BatchCargoCardRenderer = ({
  cargo,
  onAction,
  activeTab,
}: {
  cargo: CargoDisplay;
  onAction: (action: string, cargo?: CargoDisplay) => void;
  activeTab: string;
}) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-blue-600" />
          <div>
            <h3 className="font-medium text-gray-900 font-mono text-sm">
              {cargo.trackingNumber}
            </h3>
            {cargo.chinaTrackingNumber !== "N/A" && (
              <p className="text-xs text-gray-500">
                China: {cargo.chinaTrackingNumber}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAction("qr-code", cargo)}
            className="h-8 w-8 p-0"
          >
            <QrCode className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAction("view", cargo)}>
                <GotoIcon className="h-4 w-4 mr-2" />
                Go to Cargo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("view-details", cargo)}>
                <ReceiptText className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("edit", cargo)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {activeTab === "assigned" ? (
                <DropdownMenuItem onClick={() => onAction("unassign", cargo)}>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Unassign
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => onAction("assign", cargo)}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Assign
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => onAction("delete", cargo)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Type and Status */}
      <div className="flex items-center justify-between mb-3">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {cargo.type}
        </span>
        <StatusBadge status={cargo.status} />
      </div>

      {/* Weight and CBM */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <span className="text-sm text-gray-600">Weight: </span>
          <span className="text-sm font-medium text-gray-900">
            {cargo.weight}
          </span>
        </div>
        <div>
          <span className="text-sm text-gray-600">CBM: </span>
          <span className="text-sm font-medium text-gray-900">{cargo.cbm}</span>
        </div>
      </div>

      {/* Customer */}
      <div className="mb-3">
        <span className="text-sm text-gray-600">Customer: </span>
        <span className="text-sm text-gray-900">{cargo.customer}</span>
      </div>

      {/* Description */}
      {cargo.particular && cargo.particular !== "N/A" && (
        <div className="mb-3">
          <span className="text-sm text-gray-600">Description: </span>
          <span className="text-sm text-gray-900">{cargo.particular}</span>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Container className="h-3 w-3" />
          {cargo.date}
        </div>
      </div>
    </div>
  );
};

// Customer Card Renderer Component for Batch Page
const BatchCustomerCardRenderer = ({
  customer,
}: {
  customer: CustomerDisplay;
}) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-purple-600" />
          <div>
            <h3 className="font-medium text-gray-900">{customer.name}</h3>
            <p className="text-xs text-gray-500">{customer.email}</p>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-3 mb-3">
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600">
            {customer.cargoCount}
          </div>
          <div className="text-xs text-gray-500">Cargo Items</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-green-600">
            {customer.totalWeight.toFixed(1)}kg
          </div>
          <div className="text-xs text-gray-500">Total Weight</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-amber-600">
            {customer.totalCBM.toFixed(2)}m³
          </div>
          <div className="text-xs text-gray-500">Total CBM</div>
        </div>
      </div>

      {/* Footer */}
      <div className="pt-3 border-t border-gray-100">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {customer.cargoCount} {customer.cargoCount === 1 ? "item" : "items"}{" "}
          in batch
        </span>
      </div>
    </div>
  );
};

// Helper function to generate batch activity history
const generateBatchActivity = (
  batch: BatchWithRelations
): BatchActivityItem[] => {
  const activity: BatchActivityItem[] = [];
  const createdDateTime = formatDateTime(batch.created_at);
  const updatedDateTime = formatDateTime(batch.updated_at);

  // Always show batch created
  activity.push({
    activity: "Batch Created",
    date: createdDateTime.date,
    time: createdDateTime.time,
    location: "System",
    completed: true,
    icon: Container,
    description: "Batch was registered in the system",
  });

  // Add recent activity based on cargo assignments
  if (batch.cargos && Array.isArray(batch.cargos) && batch.cargos.length > 0) {
    activity.push({
      activity: "Cargo Assigned",
      date: updatedDateTime.date,
      time: updatedDateTime.time,
      location: "Warehouse",
      completed: true,
      icon: Package,
      description: `${batch.cargos.length} cargo item(s) assigned to this batch`,
    });
  }

  return activity;
};

// Helper function to format dimensions
const formatDimensionsDisplay = (cargo: CargoWithRelations): string => {
  if (
    !cargo.dimension_length ||
    !cargo.dimension_width ||
    !cargo.dimension_height
  ) {
    return "N/A";
  }
  const unit =
    cargo.dimension_unit === "METER_CUBIC"
      ? "m"
      : cargo.dimension_unit === "FEET_CUBIC"
        ? "ft"
        : "cm";
  return `${cargo.dimension_length} × ${cargo.dimension_width} × ${cargo.dimension_height} ${unit}`;
};

// Helper function to format price
const formatPrice = (price: number | null): string => {
  if (!price) return "N/A";
  return `$${price.toLocaleString()}`;
};

// Helper function to get entity name with supplier support
const getEntityName = (cargo: CargoWithRelations): string => {
  if (cargo.customers?.name) {
    return cargo.customers.name;
  }
  if ((cargo as any).suppliers?.tracking_number) {
    return `Supplier: ${(cargo as any).suppliers.tracking_number}`;
  }
  if ((cargo as any).suppliers?.phone) {
    return `Supplier: ${(cargo as any).suppliers.phone}`;
  }
  return "Unknown";
};

// Helper function to get entity type
const getEntityType = (
  cargo: CargoWithRelations
): "customer" | "supplier" | "unknown" => {
  if (cargo.customer_id) return "customer";
  if ((cargo as any).supplier_id) return "supplier";
  return "unknown";
};

// Helper function to get entity ID
const getEntityId = (cargo: CargoWithRelations): string => {
  if (cargo.customer_id) return cargo.customer_id;
  if ((cargo as any).supplier_id) return (cargo as any).supplier_id;
  return "";
};

// Helper function to get entity phone number
const getEntityPhone = (cargo: CargoWithRelations): string => {
  if (cargo.customers?.phone) {
    return cargo.customers.phone;
  }
  if ((cargo as any).suppliers?.phone) {
    return (cargo as any).suppliers.phone;
  }
  return "";
};

// Transform cargo data for display (enhanced with new fields)
const transformCargoData = (cargo: CargoWithRelations): CargoDisplay => {
  return {
    id: cargo.id,
    trackingNumber: cargo.tracking_number || "N/A",
    chinaTrackingNumber: cargo.china_tracking_number || "N/A",
    type: cargo.type || "General", // Use dynamic type from database
    origin: "N/A", // Origin no longer available from freight
    destination: "N/A", // Destination no longer available from freight
    status: cargo.status || "Unknown",
    weight: cargo.weight_value ? `${cargo.weight_value} kg` : "N/A",
    cbm: cargo.cbm_value
      ? `${cargo.cbm_value.toFixed(2)} ${getDimensionUnitAbbreviation(cargo.cbm_unit)}`
      : "N/A",
    customer: getEntityName(cargo),
    customerId: getEntityId(cargo),
    customerPhone: getEntityPhone(cargo),
    entityType: getEntityType(cargo),
    date: cargo.created_at ? formatDate(cargo.created_at) : "N/A",
    updatedAt: cargo.updated_at ? formatDate(cargo.updated_at) : "N/A",
    particular: cargo.particular || "N/A",
    batchCode: cargo.batches?.code || "N/A",
    batchId: cargo.batch_id || "",
    // New enhanced fields
    ctn: cargo.ctn || 0,
    dimensions: formatDimensionsDisplay(cargo),
    unitPrice: formatPrice(cargo.unit_price),
    totalPrice: formatPrice(cargo.total_price),
    invoiceStatus: (cargo as any).invoices?.status || "No Invoice",
    // Assigned user information
    assignedTo: (cargo as any).users?.name.toLowerCase() || "Unassigned",
    assignedToId: cargo.assigned_to || "",
  };
};

// Cargo table columns (similar to cargo-management)
const CargoTableColumns = ({
  onAction,
  activeTab,
}: {
  onAction: (action: string, cargo?: CargoDisplay) => void;
  activeTab: string;
}) => {
  return [
    {
      key: "date",
      label: "Created",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-500">{cargo.date}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "updatedAt",
      label: "Updated",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-500">{cargo.updatedAt}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "trackingNumber",
      label: "Tracking Number",
      render: (cargo: CargoDisplay) => (
        <div
          className="font-mono text-sm text-nowrap"
          onClick={() => onAction("view", cargo)}
        >
          <div className="font-medium text-primary hover:underline cursor-pointer">
            {cargo.trackingNumber}
          </div>
          {cargo.chinaTrackingNumber !== "N/A" && (
            <div className="text-xs text-gray-500">
              China: {cargo.chinaTrackingNumber}
            </div>
          )}
        </div>
      ),
      className: "min-w-[180px]",
    },
    {
      key: "customer",
      label: "Customer/Supplier",
      render: (cargo: CargoDisplay) => (
        <div className="text-sm">
          <span className="text-gray-900">{cargo.customer}</span>
          <div className="text-xs text-gray-500 capitalize">
            {cargo.entityType}
            {cargo.customerPhone && (
              <div className="text-xs text-gray-400 mt-0.5">
                {cargo.customerPhone}
              </div>
            )}
          </div>
        </div>
      ),
      className: "min-w-[150px]",
    },
    {
      key: "assignedTo",
      label: "Assigned",
      render: (cargo: CargoDisplay) => (
        <div className="text-sm">
          <span
            className={`text-blue-700 ${cargo.assignedTo === "Unassigned" ? "text-gray-400 italic" : ""} capitalize`}
          >
            {cargo.assignedTo}
          </span>
        </div>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "particular",
      label: "Particular",
      render: (cargo: CargoDisplay) => (
        <p className="inline-flex text-wrap">{cargo.particular}</p>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "type",
      label: "Type",
      render: (cargo: CargoDisplay) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/5 text-primary border border-primary/25 text-nowrap">
          {cargo.type.toLocaleLowerCase()}
        </span>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "status",
      label: "Status",
      render: (cargo: CargoDisplay) => <StatusBadge status={cargo.status} />,
      className: "min-w-[120px]",
    },
    {
      key: "weight",
      label: "Weight",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.weight}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "cbm",
      label: "CBM",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.cbm}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "ctn",
      label: "CTN",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono">{cargo.ctn}</span>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "dimensions",
      label: "Dimensions",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.dimensions}</span>
      ),
      className: "min-w-[140px]",
    },
    {
      key: "unitPrice",
      label: "Unit Price",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono">
          {cargo.unitPrice}
        </span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "totalPrice",
      label: "Total Price",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono font-medium">
          {cargo.totalPrice}
        </span>
      ),
      className: "min-w-[110px]",
    },

    {
      key: "invoiceStatus",
      label: "Invoice",
      render: (cargo: CargoDisplay) => (
        <span
          className={`text-sm px-2 py-1 rounded-full text-xs font-medium ${
            cargo.invoiceStatus === "PAID"
              ? "bg-green-100 text-green-800"
              : cargo.invoiceStatus === "PENDING"
                ? "bg-yellow-100 text-yellow-800"
                : cargo.invoiceStatus === "OVERDUE"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
          }`}
        >
          {cargo.invoiceStatus}
        </span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      label: "Actions",
      render: (cargo: CargoDisplay) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAction("qr-code", cargo)}
            className="h-8 w-8 p-0"
          >
            <QrCode className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAction("view", cargo)}>
                <GotoIcon className="h-4 w-4 mr-2" />
                Go to Cargo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("view-details", cargo)}>
                <ReceiptText className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("edit", cargo)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {activeTab === "assigned" ? (
                <DropdownMenuItem onClick={() => onAction("unassign", cargo)}>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Unassign
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => onAction("assign", cargo)}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Assign
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => onAction("delete", cargo)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      className: "w-[100px]",
    },
  ];
};

const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon?: React.ElementType;
  className?: string;
}) => (
  <div className={className}>
    <p className="text-xs text-gray-500 mb-0.5 flex items-center gap-1">
      {Icon && <Icon size={14} className="text-gray-400" />}
      {label}
    </p>
    <p className="text-sm text-gray-900 font-medium">{value}</p>
  </div>
);

const ActivityItem = ({
  item,
  isLast,
}: {
  item: BatchActivityItem;
  isLast: boolean;
}) => {
  const IconComponent = item.icon;

  return (
    <li className="relative pb-8">
      {!isLast && (
        <div className="absolute left-[11px] top-4 -bottom-4 w-0.5 bg-gray-200" />
      )}
      <div className="relative flex items-start gap-3">
        <div
          className={cn(
            "w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 z-10",
            item.completed
              ? "bg-primary text-white"
              : "bg-gray-100 border border-gray-200 text-gray-400"
          )}
        >
          <IconComponent size={14} />
        </div>
        <div className="flex-1 pt-0.5">
          <p className="text-sm font-medium text-gray-900">{item.activity}</p>
          <p className="text-xs text-gray-500">
            {item.date} • {item.time} • {item.location}
          </p>
          {item.description && (
            <p className="text-xs text-gray-400 mt-1">{item.description}</p>
          )}
        </div>
      </div>
    </li>
  );
};

// Note: CargoTableRow component removed as we now use the Listing.Table component
// with CargoTableColumns for consistent table rendering across the application.

function BatchDetailPage() {
  const params = useParams();
  const router = useRouter();
  const batchId = params?.id as string;
  const { user: _authUser } = useAuth();

  // Early return if no batchId is available
  if (!batchId) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading batch information...</p>
        </div>
      </div>
    );
  }

  const [batch, setBatch] = useState<BatchWithRelations | null>(null);
  const [assignedCargo, setAssignedCargo] = useState<CargoWithRelations[]>([]);
  const [availableCargo, setAvailableCargo] = useState<CargoWithRelations[]>(
    []
  );
  const [assignedCargoDisplay, setAssignedCargoDisplay] = useState<
    CargoDisplay[]
  >([]);
  const [availableCargoDisplay, setAvailableCargoDisplay] = useState<
    CargoDisplay[]
  >([]);
  const [customers, setCustomers] = useState<CustomerDisplay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Note: Assignment/unassignment loading states and confirmation dialogs
  // are now handled by the centralized useCargoManagement hook
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Cargo dialog states
  const [isEditCargoDialogOpen, setIsEditCargoDialogOpen] = useState(false);
  const [selectedCargoForEdit, setSelectedCargoForEdit] =
    useState<CargoWithRelations | null>(null);
  const [isQRCodeDialogOpen, setIsQRCodeDialogOpen] = useState(false);
  const [selectedCargoForQR, setSelectedCargoForQR] = useState<any>(null);
  const [isCargoDetailsDialogOpen, setIsCargoDetailsDialogOpen] =
    useState(false);
  const [selectedCargoForDetails, setSelectedCargoForDetails] =
    useState<CargoManagementCargoDisplay | null>(null);

  const [cbmExceededDialog, setCbmExceededDialog] = useState<{
    open: boolean;
    cargoId: string;
    cargoTrackingNumber: string;
    cargoCBM: number;
  }>({
    open: false,
    cargoId: "",
    cargoTrackingNumber: "",
    cargoCBM: 0,
  });

  // New modal states
  const [isNewCargoModalOpen, setIsNewCargoModalOpen] = useState(false);
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);
  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);

  // Filter states for cargo management (unused but kept for future implementation)
  // const [assignedCargoSearchTerm, setAssignedCargoSearchTerm] = useState("");
  // const [assignedCargoColumnFilters, setAssignedCargoColumnFilters] = useState<ColumnFilter[]>([]);
  // const [availableCargoSearchTerm, setAvailableCargoSearchTerm] = useState("");
  // const [availableCargoColumnFilters, setAvailableCargoColumnFilters] = useState<ColumnFilter[]>([]);

  // Tab state
  const [activeTab, setActiveTab] = useState<string>("assigned");

  // View mode state
  const [viewMode, setViewMode] = useState<"cards" | "table">("table");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);

  // Bulk task creation state
  const [isBulkTaskDialogOpen, setIsBulkTaskDialogOpen] = useState(false);

  // Use cargo management hook for reusable bulk actions
  const cargoManagement = useCargoManagement();

  // Export dialog hook
  const cargoExportDialog = useCargoExportDialog();

  // Customer relations management hook for customer/supplier modals
  const customerRelationsManagement = useCustomerRelationsManagement();

  // Table column definitions for dynamic filtering
  const cargoTableColumns: TableColumn[] = [
    {
      key: "trackingNumber",
      label: "Tracking Number",
      type: "string",
      searchable: true,
    },
    {
      key: "particular",
      label: "Description",
      type: "string",
      searchable: true,
    },
    { key: "weight", label: "Weight", type: "string", searchable: true },
    { key: "cbm", label: "CBM", type: "string", searchable: true },
    { key: "ctn", label: "CTN", type: "string", searchable: true },
    {
      key: "dimensions",
      label: "Dimensions",
      type: "string",
      searchable: true,
    },
    { key: "unitPrice", label: "Unit Price", type: "string", searchable: true },
    {
      key: "totalPrice",
      label: "Total Price",
      type: "string",
      searchable: true,
    },
    {
      key: "customer",
      label: "Customer/Supplier",
      type: "string",
      searchable: true,
    },
    {
      key: "invoiceStatus",
      label: "Invoice Status",
      type: "enum",
      searchable: true,
    },
    { key: "status", label: "Status", type: "enum", searchable: true },
    { key: "date", label: "Date", type: "date", searchable: true },
  ];

  // Column filter handlers
  const handleColumnFilterAdd = useCallback((filter: ColumnFilter) => {
    setColumnFilters((prev) => [...prev, filter]);
  }, []);

  const handleColumnFilterRemove = useCallback((index: number) => {
    setColumnFilters((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Add timeout wrapper for each API call
  const timeoutPromise = (promise: Promise<any>, timeoutMs: number = 15000) => {
    return Promise.race([
      promise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Request timeout")), timeoutMs)
      ),
    ]);
  };

  const fetchBatchCargo = async () => {
    try {
      const [assignedCargoResult, availableCargoResult] = await Promise.all([
        timeoutPromise(cargoService.getCargosByBatch(batchId)),
        timeoutPromise(
          cargoService.getAllCargosWithRelations({
            limit: 50,
          })
        ), // Get all cargo for current account, filter in UI
      ]);

      // Handle assigned cargo result
      if (assignedCargoResult.success && assignedCargoResult.data) {
        setAssignedCargo(assignedCargoResult.data);
        setAssignedCargoDisplay(
          assignedCargoResult.data.map(transformCargoData)
        );
        console.log(
          `[BatchDetail] Loaded ${assignedCargoResult.data.length} assigned cargo items`
        );
      }

      // Handle available cargo result
      if (availableCargoResult.success && availableCargoResult.data) {
        // Filter out cargo already assigned to a batch
        setAvailableCargo(availableCargoResult.data);
        setAvailableCargoDisplay(
          availableCargoResult.data.map(transformCargoData)
        );
      }

      return { assignedCargoResult, availableCargoResult };
    } catch (error: any) {
      return { assignedCargoResult: null, availableCargoResult: null };
    }
  };

  const fetchBatchData = async () => {
    const maxRetries = 3;
    let retryCount = 0;

    const attemptFetch = async (): Promise<void> => {
      try {
        setLoading(true);
        setError(null);

        console.log(
          `[BatchDetail] Fetching data for batch ${batchId} (attempt ${retryCount + 1})`
        );

        // Fetch all data in parallel with timeout protection
        const [batchResult, cargoResults] = await Promise.all([
          timeoutPromise(batchService.getBatchWithRelations(batchId)),
          fetchBatchCargo(),
        ]);

        console.log("[BatchDetail] API calls completed successfully");

        // Handle batch result
        if (!batchResult.success || !batchResult.data) {
          throw new Error(batchResult.error || "Batch not found");
        }
        setBatch(batchResult.data);

        // Generate customer data from assigned cargo
        if (
          cargoResults.assignedCargoResult.success &&
          cargoResults.assignedCargoResult.data
        ) {
          const customerMap = new Map<string, CustomerDisplay>();
          cargoResults.assignedCargoResult.data.forEach(
            (cargo: CargoWithRelations) => {
              if (cargo.customers) {
                const customerId = cargo.customers.id;
                if (customerMap.has(customerId)) {
                  const existing = customerMap.get(customerId)!;
                  existing.cargoCount += 1;
                  existing.totalWeight += cargo.weight_value || 0;
                  existing.totalCBM += cargo.cbm_value || 0;
                } else {
                  customerMap.set(customerId, {
                    id: customerId,
                    name: cargo.customers.name || "Unknown",
                    email: cargo.customers.email || "N/A",
                    cargoCount: 1,
                    totalWeight: cargo.weight_value || 0,
                    totalCBM: cargo.cbm_value || 0,
                  });
                }
              }
            }
          );
          setCustomers(Array.from(customerMap.values()));
        }

        console.log("[BatchDetail] Data loading completed successfully");
      } catch (error: any) {
        console.error(
          `[BatchDetail] Error on attempt ${retryCount + 1}:`,
          error
        );

        // Check if it's a network error that we should retry
        const isRetryableError =
          error.message?.includes("NetworkError") ||
          error.message?.includes("timeout") ||
          error.message?.includes("fetch") ||
          error.name === "TypeError" ||
          error.code === "NETWORK_ERROR";

        if (isRetryableError && retryCount < maxRetries - 1) {
          retryCount++;
          const delay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff
          console.log(`[BatchDetail] Retrying in ${delay}ms...`);

          await new Promise((resolve) => setTimeout(resolve, delay));
          return attemptFetch();
        }

        // Final error handling
        const errorMessage = error.message?.includes("timeout")
          ? "Request timed out. Please check your connection and try again."
          : error.message?.includes("NetworkError")
            ? "Network error occurred. Please check your connection and try again."
            : `Failed to fetch batch information: ${error.message || "Unknown error"}`;

        setError(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    };

    if (batchId) {
      attemptFetch().catch(() => {
        // Error already handled in attemptFetch
      });
    }
  };

  const refreshBatchData = async () => {
    await fetchBatchData();
  };

  const refreshCargoData = async () => {
    await fetchBatchCargo();
  };

  // Handle edit cargo action
  const handleEditCargo = async (cargoId: string) => {
    try {
      // Fetch full cargo data with relations
      const result = await cargoService.getCargoWithRelations(cargoId);
      if (result.success && result.data) {
        setSelectedCargoForEdit(result.data);
        setIsEditCargoDialogOpen(true);
      } else {
        toast.error("Failed to load cargo details for editing");
      }
    } catch (error) {
      console.error("Error fetching cargo for edit:", error);
      toast.error("Failed to load cargo details");
    }
  };

  useEffect(() => {
    fetchBatchData();
  }, [batchId]);

  // Memoized CBM metrics calculation

  const cbmMetrics = useMemo(() => {
    if (!batch) return { usedCBM: 0, totalCapacity: 0, remainingCBM: 0 };

    const usedCBM = calculateTotalCBM(
      assignedCargo as Array<{ cbm_value?: number | null; cbm_unit?: string }>
    );
    const totalCapacity = batch.cbm_value || 0;
    const remainingCBM = Math.max(0, totalCapacity - usedCBM);

    return { usedCBM, totalCapacity, remainingCBM };
  }, [batch, assignedCargo]);

  // Memoized Weight metrics calculation
  const weightMetrics = useMemo(() => {
    if (!batch) return { usedWeight: 0, totalCapacity: 0, remainingWeight: 0 };

    const usedWeight = calculateTotalWeight(
      assignedCargo as Array<{
        weight_value?: number | null;
        weight_unit?: string;
      }>
    );
    const totalCapacity = batch.weight || 0;
    const remainingWeight = Math.max(0, totalCapacity - usedWeight);

    return { usedWeight, totalCapacity, remainingWeight };
  }, [batch, assignedCargo]);

  // Note: matchesFilter function removed as filtering is now handled
  // by the unified filteredData function which uses simpler string matching

  // Note: Individual tab filtering is handled by the main filteredData function
  // which switches data based on activeTab. This provides a unified filtering
  // experience across all tabs.

  // Get current data based on active tab for FilterPanel
  const getCurrentData = useCallback(() => {
    switch (activeTab) {
      case "assigned":
        return assignedCargoDisplay;
      case "available":
        return availableCargoDisplay;
      case "customers":
        return customers;
      default:
        return [];
    }
  }, [activeTab, assignedCargoDisplay, availableCargoDisplay, customers]);

  // Apply search and column filters to current data for FilterPanel
  const filteredData = useMemo(() => {
    let data: any[] = getCurrentData();

    // Apply search term filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      data = data.filter((item: any) => {
        // For cargo items
        if (item.trackingNumber) {
          return (
            item.trackingNumber.toLowerCase().includes(searchLower) ||
            item.chinaTrackingNumber?.toLowerCase().includes(searchLower) ||
            item.particular?.toLowerCase().includes(searchLower) ||
            item.customer?.toLowerCase().includes(searchLower) ||
            item.type?.toLowerCase().includes(searchLower) ||
            item.status?.toLowerCase().includes(searchLower)
          );
        }
        // For customer items
        if (item.name) {
          return (
            item.name.toLowerCase().includes(searchLower) ||
            item.email?.toLowerCase().includes(searchLower)
          );
        }
        return false;
      });
    }

    // Apply column filters
    if (columnFilters.length > 0) {
      data = data.filter((item: any) => {
        return columnFilters.every((filter) => {
          const itemValue = item[filter.column];
          if (itemValue === null || itemValue === undefined) return false;
          return String(itemValue)
            .toLowerCase()
            .includes(filter.value.toLowerCase());
        });
      });
    }

    return data;
  }, [getCurrentData, searchTerm, columnFilters]);

  // Memoized function to check if cargo assignment would exceed CBM capacity
  const checkCBMCapacity = useCallback(
    (cargo: CargoWithRelations): boolean => {
      const cargoCBM = cargo.cbm_value || 0;
      return cbmMetrics.usedCBM + cargoCBM > cbmMetrics.totalCapacity;
    },
    [cbmMetrics]
  );

  // Note: Individual cargo assignment/unassignment functions have been migrated
  // to the centralized useCargoManagement hook and are now handled through
  // the handleCargoAction function with "assign" and "unassign" actions.

  // Handle batch update
  const handleBatchUpdated = async () => {
    try {
      setLoading(true);
      // Refresh batch data
      const batchResult = await batchService.getBatchWithRelations(batchId);
      if (batchResult.success && batchResult.data) {
        setBatch(batchResult.data);
      }

      // Refresh Data
      refreshBatchData();
    } catch (error) {
      console.error("Error refreshing batch:", error);
    } finally {
      setLoading(false);
    }
  };

  // Reset pagination when switching tabs or when search/filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchTerm, columnFilters]);

  // Handle customer/supplier creation success
  const handleEntityCreated = () => {
    // This will trigger a refresh of the cargo form's customer/supplier lists
    // The forms handle their own data refreshing
  };

  // Wrapper functions to integrate cargo management hook with local state
  const handleBulkStatusUpdate = useCallback(
    (status: string) => {
      const selectedIds = Array.from(cargoManagement.state.selectedCargos);
      if (selectedIds.length === 0) {
        toast.error("Please select cargo items to update");
        return;
      }

      // Use the robust implementation from cargo management
      cargoManagement.handleBulkStatusUpdate(status);
      // Refresh local data to show updated statuses
      refreshCargoData();
    },
    [cargoManagement]
  );

  const handleBulkDelete = useCallback(() => {
    const selectedIds = Array.from(cargoManagement.state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to delete");
      return;
    }

    // Use the robust implementation from cargo management
    cargoManagement.handleBulkDelete();
    // Refresh local data to show updated assignments
    refreshCargoData();
  }, [cargoManagement]);

  const handleBulkAssign = useCallback(async () => {
    const selectedIds = Array.from(cargoManagement.state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to assign");
      return;
    }

    if (!batch?.id) {
      toast.error("Batch ID not available");
      return;
    }

    // Use the proven implementation from cargo management
    await cargoManagement.handleBulkAssignToBatch(batch.id);
    // Refresh local data to show updated assignments
    refreshCargoData();
  }, [cargoManagement, batch?.id]);

  const handleBulkUnassign = useCallback(async () => {
    const selectedIds = Array.from(cargoManagement.state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to unassign");
      return;
    }

    // Use the proven implementation from cargo management
    await cargoManagement.handleBulkUnassignFromBatch();
    // Refresh local data to show updated assignments
    refreshCargoData();
  }, [cargoManagement]);

  const handleBulkCreateInvoices = useCallback(async () => {
    if (!cargoManagement.state.selectedCargos.size) {
      toast.error("Please select cargo items to create invoices from batch");
      return;
    }

    // Use the proven implementation from cargo management
    await cargoManagement.handleBulkCreateInvoice();
    // Refresh local data to show updated invoice statuses
    refreshCargoData();
  }, [cargoManagement]);

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    cargoManagement.updateState({ selectedCargos: new Set() });
    toast.success("Selections cleared");
  }, [cargoManagement]);

  // Bulk task creation handler
  const handleBulkCreateTasks = useCallback(() => {
    const selectedIds = Array.from(cargoManagement.state.selectedCargos);
    if (selectedIds.length === 0) {
      toast.error("Please select cargo items to create tasks for");
      return;
    }
    setIsBulkTaskDialogOpen(true);
  }, [cargoManagement.state.selectedCargos]);

  // Handle task creation success
  const handleTasksCreated = useCallback(() => {
    cargoManagement.updateState({ selectedCargos: new Set() });
    toast.success("Task created successfully with multiple associations");
  }, [cargoManagement]);

  // Prepare selected items for bulk task creation
  const selectedItemsForTasks = useMemo(() => {
    const currentData = getCurrentData();
    return Array.from(cargoManagement.state.selectedCargos)
      .map((cargoId) => {
        const item = currentData.find((item) => item.id === cargoId);
        if (!item) return null;

        // Check if it's a CargoDisplay (has trackingNumber property)
        const isCargoDisplay = "trackingNumber" in item;
        if (!isCargoDisplay) return null;

        const cargo = item as CargoDisplay;
        return {
          id: cargo.id,
          name: cargo.trackingNumber || cargo.id,
          identifier: cargo.trackingNumber,
        };
      })
      .filter(Boolean) as Array<{
      id: string;
      name: string;
      identifier?: string;
    }>;
  }, [cargoManagement.state.selectedCargos, getCurrentData]);

  // Export handler based on active tab
  const handleExport = useCallback(() => {
    const currentData = getCurrentData();

    if (!currentData || currentData.length === 0) {
      toast.error("No data available to export");
      return;
    }

    const batchCode = batch?.code || "unknown";
    const timestamp = new Date().toISOString().split("T")[0];

    switch (activeTab) {
      case "assigned":
        cargoExportDialog.openExportDialog(currentData, {
          title: `Assigned Cargo - Batch ${batchCode}`,
          filename: `batch_${batchCode}_assigned_cargo_${timestamp}`,
          excludeColumns: ["metadata", "attachments"],
          columnMapping: {
            tracking_number: "Tracking Number",
            china_tracking_number: "China Tracking",
            description: "Description",
            customer_name: "Customer",
            weight_value: "Weight (kg)",
            cbm_value: "Volume (m³)",
            quantity: "Quantity",
            status: "Status",
            code: "Batch Code",
            created_at: "Created Date",
          },
          onSuccess: (type: "excel" | "pdf", filename: string) => {
            toast.success(
              `${type.toUpperCase()} export completed: ${filename}`
            );
          },
          onError: (error: string) => {
            toast.error(`Export failed: ${error}`);
          },
        });
        break;
      case "available":
        cargoExportDialog.openExportDialog(currentData, {
          title: `Available Cargo - Batch ${batchCode}`,
          filename: `batch_${batchCode}_available_cargo_${timestamp}`,
          excludeColumns: ["metadata", "attachments"],
          columnMapping: {
            tracking_number: "Tracking Number",
            china_tracking_number: "China Tracking",
            description: "Description",
            customer_name: "Customer",
            weight_value: "Weight (kg)",
            cbm_value: "Volume (m³)",
            quantity: "Quantity",
            status: "Status",
            code: "Batch Code",
            created_at: "Created Date",
          },
          onSuccess: (type: "excel" | "pdf", filename: string) => {
            toast.success(
              `${type.toUpperCase()} export completed: ${filename}`
            );
          },
          onError: (error: string) => {
            toast.error(`Export failed: ${error}`);
          },
        });
        break;
      case "customers":
        cargoExportDialog.openExportDialog(currentData, {
          title: `Customers - Batch ${batchCode}`,
          filename: `batch_${batchCode}_customers_${timestamp}`,
          excludeColumns: ["id"],
          columnMapping: {
            name: "Customer Name",
            email: "Email Address",
            cargoCount: "Cargo Items",
            totalWeight: "Total Weight (kg)",
            totalCBM: "Total CBM (m³)",
          },
          onSuccess: (type: "excel" | "pdf", filename: string) => {
            toast.success(
              `${type.toUpperCase()} export completed: ${filename}`
            );
          },
          onError: (error: string) => {
            toast.error(`Export failed: ${error}`);
          },
        });
        break;
      default:
        toast.error("Export not available for this tab");
        break;
    }
  }, [activeTab, getCurrentData, batch?.code, cargoExportDialog]);

  // Render bulk actions based on active tab
  const renderBulkActions = useCallback(() => {
    const actions = [];

    // Common actions for all tabs with cargo
    if (activeTab === "assigned" || activeTab === "available") {
      actions.push(
        <BulkStatusSelector
          key="status"
          statusOptions={CARGO_STATUS_OPTIONS}
          onStatusUpdate={handleBulkStatusUpdate}
          disabled={cargoManagement.state.bulkActionLoading}
          placeholder="Mark as..."
        />
      );
    }

    // Tab-specific actions
    if (activeTab === "assigned") {
      actions.push(
        <Button
          key="unassign"
          variant="outline"
          size="sm"
          onClick={handleBulkUnassign}
          className="gap-2"
          disabled={cargoManagement.state.bulkActionLoading}
        >
          <UserMinus size={16} />
          Unassign
        </Button>
      );

      // Create Invoices action for assigned cargo
      actions.push(
        <Button
          key="create-invoices"
          variant="outline"
          size="sm"
          onClick={handleBulkCreateInvoices}
          className="gap-2"
          disabled={cargoManagement.state.bulkActionLoading}
        >
          <Receipt size={16} />
          Create Invoices
        </Button>
      );
    }

    if (activeTab === "available") {
      actions.push(
        <Button
          key="assign"
          variant="outline"
          size="sm"
          onClick={handleBulkAssign}
          className="gap-2"
          disabled={cargoManagement.state.bulkActionLoading}
        >
          <UserPlus size={16} />
          Assign to Batch
        </Button>
      );
    }

    // Create Tasks action for both tabs
    if (activeTab === "assigned" || activeTab === "available") {
      actions.push(
        <Button
          key="create-tasks"
          variant="outline"
          size="sm"
          onClick={handleBulkCreateTasks}
          className="gap-2"
          disabled={cargoManagement.state.bulkActionLoading}
        >
          <CheckSquare size={16} />
          Create Tasks
        </Button>
      );
    }

    // Delete action for both tabs
    if (activeTab === "assigned" || activeTab === "available") {
      actions.push(
        <Button
          key="delete"
          variant="destructive"
          size="sm"
          onClick={handleBulkDelete}
          className="gap-2"
          disabled={cargoManagement.state.bulkActionLoading}
        >
          <Trash2 size={16} />
          Delete
        </Button>
      );
    }

    return actions;
  }, [
    activeTab,
    handleBulkStatusUpdate,
    handleBulkUnassign,
    handleBulkAssign,
    handleBulkDelete,
    handleBulkCreateInvoices,
    handleBulkCreateTasks,
    cargoManagement.state.bulkActionLoading,
  ]);

  // Handle cargo actions (extending cargo-management with batch-specific actions)
  const handleCargoAction = useCallback(
    (action: string, cargo?: CargoDisplay) => {
      // Handle batch-specific actions first
      switch (action) {
        case "assign":
          if (cargo && batch?.id) {
            // Use centralized assignment function with CBM validation callback
            const cargoToAssign = availableCargo.find((c) => c.id === cargo.id);
            if (cargoToAssign) {
              const wouldExceedCapacity = checkCBMCapacity(cargoToAssign);
              if (wouldExceedCapacity) {
                // Show CBM exceeded dialog
                setCbmExceededDialog({
                  open: true,
                  cargoId: cargo.id,
                  cargoTrackingNumber: cargoToAssign.tracking_number,
                  cargoCBM: cargoToAssign.cbm_value || 0,
                });
                return;
              }
            }

            // Use centralized assignment function
            cargoManagement.handleAssignCargoToBatch(cargo.id, batch.id, {
              skipConfirmation: false,
              onSuccess: () => {
                // Update local state to reflect the assignment
                const cargoToMove = availableCargo.find(
                  (c) => c.id === cargo.id
                );
                if (cargoToMove) {
                  const assignedCargo = { ...cargoToMove, batch_id: batch.id };
                  setAssignedCargo((prev) => [...prev, assignedCargo]);
                  setAvailableCargo((prev) =>
                    prev.filter((c) => c.id !== cargo.id)
                  );

                  // Refresh Cargo Data
                  refreshCargoData();
                }
              },
              onError: (error) => {
                console.error("Assignment failed:", error);
              },
            });
          }
          return;
        case "unassign":
          if (cargo) {
            // Use centralized unassignment function
            cargoManagement.handleUnassignCargoFromBatch(cargo.id, {
              skipConfirmation: false,
              onSuccess: () => {
                // Update local state to reflect the unassignment
                const cargoToMove = assignedCargo.find(
                  (c) => c.id === cargo.id
                );
                if (cargoToMove) {
                  const unassignedCargo = { ...cargoToMove, batch_id: null };
                  setAvailableCargo((prev) => [...prev, unassignedCargo]);
                  setAssignedCargo((prev) =>
                    prev.filter((c) => c.id !== cargo.id)
                  );

                  // Refresh Cargo Data
                  refreshCargoData();
                }
              },
              onError: (error) => {
                console.error("Unassignment failed:", error);
              },
            });
          }
          return;
        case "edit":
          if (cargo) {
            // Fetch full cargo data for editing
            handleEditCargo(cargo.id);
          }
          return;
        case "qr-code":
          if (cargo) {
            // Convert to cargo-management CargoDisplay format for QR code
            const qrCargoData = {
              ...cargo,
              ctn: cargo.ctn || 0,
              dimensions: cargo.dimensions || "N/A",
              unitPrice: cargo.unitPrice || "0",
              totalPrice: cargo.totalPrice || "0",
              invoiceStatus: cargo.invoiceStatus || "PENDING",
            };
            setSelectedCargoForQR(qrCargoData as any);
            setIsQRCodeDialogOpen(true);
          }
          return;
        case "view":
          if (cargo) {
            // Navigate to cargo detail page
            redirect(`/cargo-management/${cargo.id}`);
          }
          return;
        case "view-details":
          if (cargo) {
            // Open cargo details dialog - ensure all required fields are present
            const cargoForDetails = {
              ...cargo,
              ctn: cargo.ctn || 0, // Ensure ctn is always a number
              dimensions: cargo.dimensions || "N/A",
              unitPrice: cargo.unitPrice || "0",
              totalPrice: cargo.totalPrice || "0",
              invoiceStatus: cargo.invoiceStatus || "PENDING",
            };
            setSelectedCargoForDetails(cargoForDetails);
            setIsCargoDetailsDialogOpen(true);
          }
          return;
        case "delete":
          // Override delete behavior for batch context
          if (cargo) {
            toast.error("Delete functionality not available from batch view");
          }
          return;
        default:
          // For any other actions, we can handle them here or show a message
          console.log(`Action "${action}" not implemented for batch context`);
          break;
      }
    },
    [
      cargoManagement,
      batch?.id,
      availableCargo,
      assignedCargo,
      checkCBMCapacity,
    ]
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading batch information...</p>
        </div>
      </div>
    );
  }

  if (error || !batch) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4 max-w-md">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Batch Not Found
            </h1>
            <p className="text-gray-600 mt-2">
              {error || "The requested batch could not be found."}
            </p>
          </div>
          <Button
            onClick={() => router.push("/batch-management")}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Batch Management
          </Button>
        </div>
      </div>
    );
  }

  const batchType = getBatchTypeDisplay(batch.type);
  const batchActivity = generateBatchActivity(batch);

  const Summary = () => {
    return (
      <div className="h-full grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="h-full lg:col-span-2 space-y-8">
          {/* Batch Information */}
          <div className="h-full p-6 bg-white border border-gray-200 rounded-lg">
            <div className="flex justify-between items-center mb-5">
              <h2 className="text-lg font-semibold text-gray-900">
                Batch Information
              </h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-7">
              <InfoItem label="Name" value={batch.name || "N/A"} icon={Info} />
              <InfoItem
                label="Code"
                value={formatBatchCode(batch.code)}
                icon={Hash}
              />
              <InfoItem
                label="Type"
                value={batchType.type}
                icon={batchType.icon}
              />
              <InfoItem
                label="Status"
                value={
                  <Badge
                    variant={
                      batch.status === "ACTIVE" ? "default" : "secondary"
                    }
                  >
                    {batch.status || "Unknown"}
                  </Badge>
                }
                icon={CheckCircle}
              />
              <InfoItem
                label="Dimensions"
                value={formatDimensions(
                  batch.length,
                  batch.width,
                  batch.height,
                  batch.cbm_unit
                )}
                icon={Ruler}
              />
              <InfoItem
                label="CBM"
                value={formatCBM(batch.cbm_value, batch.cbm_unit)}
                icon={Box}
              />
              <InfoItem
                label="Weight"
                value={formatWeight(batch.weight)}
                icon={Weight}
              />
              <InfoItem
                label="Created"
                value={formatDate(batch.created_at)}
                icon={Clock}
              />
              <InfoItem
                label="Last Updated"
                value={formatDate(batch.updated_at)}
                icon={Clock}
              />
              <InfoItem
                label="Assigned Cargo"
                value={assignedCargo.length.toString()}
                icon={Package}
              />
              {/* Freight reference removed - batches are now independent */}
              <InfoItem
                label="USD/TZS Rate"
                value={`1 USD -> ${batch.currency_conv_rate} TZS` || "N/A"}
                icon={Landmark}
              />
              <InfoItem
                label="Bill of Lading"
                value={batch.bill_of_lading || "N/A"}
                icon={FileText}
              />
              <InfoItem
                label="Category"
                value={batch.category || "N/A"}
                icon={batch.category === "SAFE" ? Shield : Skull}
              />
              <InfoItem
                label="Type"
                value={batch.freight_type || "N/A"}
                icon={batch.freight_type === "AIR" ? Plane : Ship}
              />
            </div>
          </div>
        </div>
        {/* Right Column */}
        <div className="space-y-4">
          {/* Conditional Summary based on Freight Type */}
          <>
            {/* CBM Summary for SEA freight */}
            <CBMSummaryCard
              usedCBM={cbmMetrics.usedCBM}
              totalCapacity={cbmMetrics.totalCapacity}
              cargoCount={assignedCargo.length}
              unit={batch.cbm_unit}
            />
            {/* CBM Capacity Alert */}
            <CBMCapacityAlert
              usedCBM={cbmMetrics.usedCBM}
              totalCapacity={cbmMetrics.totalCapacity}
              unit={batch.cbm_unit}
              showAlert={true}
            />
          </>
          <>
            {/* Weight Summary for AIR freight */}
            <WeightSummaryCard
              usedWeight={weightMetrics.usedWeight}
              totalCapacity={weightMetrics.totalCapacity}
              cargoCount={assignedCargo.length}
              unit={batch.weight_unit || "KILOGRAMS"}
            />
            {/* Weight Capacity Alert */}
            <WeightCapacityAlert
              usedWeight={weightMetrics.usedWeight}
              totalCapacity={weightMetrics.totalCapacity}
              unit={batch.weight_unit || "KILOGRAMS"}
              showAlert={true}
            />
          </>
        </div>
      </div>
    );
  };

  return (
    <PageTransition className="p-8">
      <Overview className="space-y-8">
        <Overview.Header
          title={`Batch Management`}
          caption={`${batchType.type} • ${formatBatchCode(batch.code)} • ${assignedCargo.length} cargo items`}
          actions={
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (batchId) {
                    setIsNewCargoModalOpen(true);
                  } else {
                    console.error("No batch ID available");
                  }
                }}
                className="gap-2"
                disabled={!batchId}
              >
                <Package size={16} />
                New Cargo
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomerModalOpen(true)}
                className="gap-2"
              >
                <Users size={16} />
                New Customer
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSupplierModalOpen(true)}
                className="gap-2"
              >
                <Truck size={16} />
                New Supplier
              </Button>
              <ProtectedEditButton entity="batches">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditDialogOpen(true)}
                  className="gap-2"
                >
                  <PencilLine size={16} />
                  Edit Batch
                </Button>
              </ProtectedEditButton>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
                className="gap-2"
              >
                <RefreshCw size={16} />
                Refresh
              </Button>
            </div>
          }
        />

        <Overview.Statistics>
          <Listing.StatCard
            icon={Package}
            name="Assigned Cargo"
            value={assignedCargo.length}
            caption="Items in this batch"
            color="blue"
          />
          <Listing.StatCard
            icon={Weight}
            name="Total Weight (kg)"
            value={batch.weight}
            caption="Batch capacity"
            color="green"
          />
          <Listing.StatCard
            icon={Box}
            name="Total CBM (m³)"
            value={formatCBM(batch.cbm_value, batch.cbm_unit)}
            caption="Volume capacity"
            color="amber"
          />
          <Listing.StatCard
            icon={Box}
            name="Total CTN"
            value={assignedCargo.reduce(
              (sum, cargo) => sum + (cargo.ctn || 0),
              0
            )}
            caption="Container count"
            color="blue"
          />
          <Listing.StatCard
            icon={DollarSign}
            name="Utilization"
            value={`${Math.round(calculateCBMUtilization(cbmMetrics.usedCBM, cbmMetrics.totalCapacity))}%`}
            caption="CBM utilization"
            color={
              cbmMetrics.usedCBM > cbmMetrics.totalCapacity ? "red" : "primary"
            }
          />
        </Overview.Statistics>

        <Overview.Content>
          {/* batch Summary */}
          <Summary />

          {/* Filter Panel */}
          <FilterPanel
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            columnFilters={columnFilters}
            onColumnFilterAdd={handleColumnFilterAdd}
            onColumnFilterRemove={handleColumnFilterRemove}
            enableDynamicFilters={true}
            columns={cargoTableColumns}
            tableData={filteredData}
            defaultFilterColumn="trackingNumber"
            autoSelectDefaultColumn={true}
            onRefresh={async () => {
              // Refresh data based on active tab
              await refreshCargoData();
            }}
            loading={loading}
            bulkActions={renderBulkActions()}
            selectedCount={cargoManagement.state.selectedCargos.size}
            showBulkActions={
              activeTab === "assigned" || activeTab === "available"
            }
            onClearSelections={handleClearSelections}
          />

          {/* Cargo and Customer Management */}
          <Listing>
            <Listing.Controls
              entity="Batch cargo"
              length={filteredData.length}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              categoryFilter={activeTab}
              onCategoryFilterChange={setActiveTab}
              categories={[
                { key: "assigned", label: "Assigned Cargo" },
                { key: "available", label: "Available Cargo" },
                { key: "customers", label: "Customers" },
                { key: "history", label: "History" },
              ]}
              actions={
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mr-4 gap-2"
                    onClick={handleExport}
                    disabled={filteredData.length === 0}
                  >
                    <Download className="h-4 w-4" />
                    Export{" "}
                    {activeTab === "assigned"
                      ? "Assigned"
                      : activeTab === "available"
                        ? "Available"
                        : activeTab === "customers"
                          ? "Customers"
                          : "Data"}
                  </Button>
                  <ProtectedCreateButton entity="cargo">
                    <Button onClick={() => setIsNewCargoModalOpen(true)}>
                      <Plus size={16} />
                      Assign New Cargo
                    </Button>
                  </ProtectedCreateButton>
                </div>
              }
            />

            {/* Conditional rendering based on active tab */}
            {activeTab === "assigned" && (
              <>
                {viewMode === "cards" ? (
                  <Listing.Cards
                    data={filteredData}
                    loading={loading}
                    renderCard={(cargo: CargoDisplay) => (
                      <BatchCargoCardRenderer
                        cargo={cargo}
                        onAction={handleCargoAction}
                        activeTab={activeTab}
                      />
                    )}
                    emptyState={
                      <div className="text-center py-12">
                        <Package
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">No Cargo Assigned</p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No cargo matches your search criteria."
                            : "This batch doesn't have any cargo assigned yet."}
                        </p>
                      </div>
                    }
                  />
                ) : (
                  <Listing.Table
                    data={filteredData}
                    columns={CargoTableColumns({
                      onAction: handleCargoAction,
                      activeTab,
                    })}
                    loading={loading}
                    enableCheckboxes={true}
                    selectedRowIds={Array.from(
                      cargoManagement.state.selectedCargos
                    )}
                    onSelectionChange={(selectedIds) =>
                      cargoManagement.updateState({
                        selectedCargos: new Set(selectedIds),
                      })
                    }
                    getRowId={(item) => item.id}
                    pagination={{
                      currentPage,
                      totalPages: Math.ceil(
                        filteredData.length / ITEMS_PER_PAGE
                      ),
                      totalItems: filteredData.length,
                      itemsPerPage: ITEMS_PER_PAGE,
                      onPageChange: setCurrentPage,
                    }}
                    emptyState={
                      <div className="text-center py-12">
                        <Package
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">No Cargo Assigned</p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No cargo matches your search criteria."
                            : "This batch doesn't have any cargo assigned yet."}
                        </p>
                      </div>
                    }
                  />
                )}
              </>
            )}

            {activeTab === "available" && (
              <>
                {viewMode === "cards" ? (
                  <Listing.Cards
                    data={filteredData}
                    loading={loading}
                    renderCard={(cargo: CargoDisplay) => (
                      <BatchCargoCardRenderer
                        cargo={cargo}
                        onAction={handleCargoAction}
                        activeTab={activeTab}
                      />
                    )}
                    emptyState={
                      <div className="text-center py-12">
                        <Package
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">
                          No Available Cargo
                        </p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No cargo matches your search criteria."
                            : "All cargo items are currently assigned to batches."}
                        </p>
                      </div>
                    }
                  />
                ) : (
                  <Listing.Table
                    data={filteredData}
                    columns={CargoTableColumns({
                      onAction: handleCargoAction,
                      activeTab,
                    })}
                    loading={loading}
                    enableCheckboxes={true}
                    selectedRowIds={Array.from(
                      cargoManagement.state.selectedCargos
                    )}
                    onSelectionChange={(selectedIds) =>
                      cargoManagement.updateState({
                        selectedCargos: new Set(selectedIds),
                      })
                    }
                    getRowId={(item) => item.id}
                    pagination={{
                      currentPage,
                      totalPages: Math.ceil(
                        filteredData.length / ITEMS_PER_PAGE
                      ),
                      totalItems: filteredData.length,
                      itemsPerPage: ITEMS_PER_PAGE,
                      onPageChange: setCurrentPage,
                    }}
                    emptyState={
                      <div className="text-center py-12">
                        <Package
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">
                          No Available Cargo
                        </p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No cargo matches your search criteria."
                            : "All cargo items are currently assigned to batches."}
                        </p>
                      </div>
                    }
                  />
                )}
              </>
            )}

            {activeTab === "customers" && (
              <>
                {viewMode === "cards" ? (
                  <Listing.Cards
                    data={filteredData}
                    loading={loading}
                    renderCard={(customer: CustomerDisplay) => (
                      <BatchCustomerCardRenderer customer={customer} />
                    )}
                    emptyState={
                      <div className="text-center py-12">
                        <Users
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">No Customers</p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No customers match your search criteria."
                            : "No customers have cargo assigned to this batch."}
                        </p>
                      </div>
                    }
                  />
                ) : (
                  <Listing.Table
                    data={filteredData}
                    columns={[
                      {
                        key: "name",
                        label: "Customer Name",
                        render: (customer: CustomerDisplay) => (
                          <div className="font-medium text-gray-900">
                            {customer.name}
                          </div>
                        ),
                      },
                      {
                        key: "email",
                        label: "Email",
                        render: (customer: CustomerDisplay) => (
                          <div className="text-sm text-gray-500">
                            {customer.email}
                          </div>
                        ),
                      },
                      {
                        key: "cargoCount",
                        label: "Cargo Items",
                        render: (customer: CustomerDisplay) => (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {customer.cargoCount}
                          </span>
                        ),
                      },
                      {
                        key: "totalWeight",
                        label: "Total Weight",
                        render: (customer: CustomerDisplay) => (
                          <span className="text-sm text-gray-900">
                            {customer.totalWeight.toFixed(2)} kg
                          </span>
                        ),
                      },
                      {
                        key: "totalCBM",
                        label: "Total CBM",
                        render: (customer: CustomerDisplay) => (
                          <span className="text-sm text-gray-900">
                            {customer.totalCBM.toFixed(2)} m³
                          </span>
                        ),
                      },
                    ]}
                    loading={loading}
                    pagination={{
                      currentPage,
                      totalPages: Math.ceil(
                        filteredData.length / ITEMS_PER_PAGE
                      ),
                      totalItems: filteredData.length,
                      itemsPerPage: ITEMS_PER_PAGE,
                      onPageChange: setCurrentPage,
                    }}
                    emptyState={
                      <div className="text-center py-12">
                        <Users
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p className="text-lg font-medium">No Customers</p>
                        <p className="text-sm text-gray-500">
                          {searchTerm || columnFilters.length > 0
                            ? "No customers match your search criteria."
                            : "No customers have cargo assigned to this batch."}
                        </p>
                      </div>
                    }
                  />
                )}
              </>
            )}

            {activeTab === "history" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Activity History
                  </h3>
                  <Badge variant="secondary">
                    {batchActivity.length} Events
                  </Badge>
                </div>

                <ul className="space-y-4">
                  {batchActivity.map((item, index) => (
                    <ActivityItem
                      key={index}
                      item={item}
                      isLast={index === batchActivity.length - 1}
                    />
                  ))}
                </ul>
              </div>
            )}
          </Listing>
        </Overview.Content>
      </Overview>

      {/* Edit Batch Dialog */}
      <EditBatchDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        batch={batch}
        onBatchUpdated={handleBatchUpdated}
        freights={[]}
      />

      {/* Note: Confirmation dialogs are now handled by the centralized useCargoManagement hook */}

      {/* CBM Exceeded Dialog */}
      <CBMExceededDialog
        open={cbmExceededDialog.open}
        onOpenChange={(open) =>
          setCbmExceededDialog({ ...cbmExceededDialog, open })
        }
        cargoTrackingNumber={cbmExceededDialog.cargoTrackingNumber}
        cargoCBM={cbmExceededDialog.cargoCBM}
        usedCBM={cbmMetrics.usedCBM}
        totalCapacity={cbmMetrics.totalCapacity}
        unit={batch.cbm_unit}
        onConfirm={() => {
          if (batch?.id) {
            cargoManagement.handleAssignCargoToBatch(
              cbmExceededDialog.cargoId,
              batch.id,
              {
                skipConfirmation: true, // Skip confirmation since user already confirmed via CBM dialog
                onSuccess: () => {
                  // Update local state to reflect the assignment
                  const cargoToMove = availableCargo.find(
                    (c) => c.id === cbmExceededDialog.cargoId
                  );
                  if (cargoToMove) {
                    const assignedCargo = {
                      ...cargoToMove,
                      batch_id: batch.id,
                    };
                    setAssignedCargo((prev) => [...prev, assignedCargo]);
                    setAvailableCargo((prev) =>
                      prev.filter((c) => c.id !== cbmExceededDialog.cargoId)
                    );
                  }
                  setCbmExceededDialog({ ...cbmExceededDialog, open: false });
                },
                onError: (error) => {
                  console.error("Assignment failed:", error);
                },
              }
            );
          }
        }}
        onCancel={() =>
          setCbmExceededDialog({ ...cbmExceededDialog, open: false })
        }
      />

      {/* New Cargo Modal */}
      <Dialog open={isNewCargoModalOpen} onOpenChange={setIsNewCargoModalOpen}>
        <DialogContent className="sm:max-w-4xl p-0">
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Create New Cargo for Batch
            </DialogTitle>
          </DialogHeader>
          <div className="px-6 pb-6 max-h-[70vh] overflow-y-auto">
            <NewCargoForm
              batchId={batchId}
              withBatchSelect={true}
              onOpenChange={setIsNewCargoModalOpen}
              onSuccess={() => {
                fetchBatchData();
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Customer Modal */}
      <CustomerModals
        showAddModal={isCustomerModalOpen}
        setShowAddModal={setIsCustomerModalOpen}
        showEditModal={false}
        setShowEditModal={() => {}}
        editingCustomer={null}
        setEditingCustomer={() => {}}
        formData={customerRelationsManagement.formData}
        setFormData={customerRelationsManagement.setFormData}
        formLoading={customerRelationsManagement.formLoading}
        formErrors={customerRelationsManagement.formErrors}
        handleCreateCustomer={async (data) => {
          await customerRelationsManagement.handleCreateCustomer(data);
          handleEntityCreated();
        }}
        handleUpdateCustomer={customerRelationsManagement.handleUpdateCustomer}
        resetCustomerForm={customerRelationsManagement.resetCustomerForm}
      />

      {/* Supplier Modal */}
      <SupplierModals
        showAddModal={isSupplierModalOpen}
        setShowAddModal={setIsSupplierModalOpen}
        showEditModal={false}
        setShowEditModal={() => {}}
        editingSupplier={null}
        setEditingSupplier={() => {}}
        formData={customerRelationsManagement.supplierFormData}
        setFormData={customerRelationsManagement.setSupplierFormData}
        formLoading={customerRelationsManagement.supplierFormLoading}
        formErrors={customerRelationsManagement.supplierFormErrors}
        handleCreateSupplier={async (data) => {
          await customerRelationsManagement.handleCreateSupplier(data);
          handleEntityCreated();
        }}
        handleUpdateSupplier={customerRelationsManagement.handleUpdateSupplier}
        resetSupplierForm={customerRelationsManagement.resetSupplierForm}
      />

      {/* Edit Cargo Dialog */}
      <EditCargoDialog
        isOpen={isEditCargoDialogOpen}
        onOpenChange={setIsEditCargoDialogOpen}
        cargo={selectedCargoForEdit}
        onSuccess={() => {
          setIsEditCargoDialogOpen(false);
          setSelectedCargoForEdit(null);
          // Refresh cargo data
          fetchBatchCargo();
        }}
      />

      {/* QR Code Dialog */}
      <QRCodeDialog
        open={isQRCodeDialogOpen}
        cargo={selectedCargoForQR}
        onOpenChange={(open) => {
          setIsQRCodeDialogOpen(open);
          if (!open) {
            setSelectedCargoForQR(null);
          }
        }}
      />

      {/* Cargo Details Dialog */}
      <CargoDetailsDialog
        open={isCargoDetailsDialogOpen}
        cargo={selectedCargoForDetails}
        onOpenChange={(open) => {
          setIsCargoDetailsDialogOpen(open);
          if (!open) {
            setSelectedCargoForDetails(null);
          }
        }}
        onCargoUpdate={() => {
          // Refresh cargo data when status is updated
          fetchBatchCargo();
        }}
        onQRCodeAction={(cargo) => {
          // Use the cargo management hook's QR code functionality
          cargoManagement.handleCargoAction("qr-code", cargo);
        }}
      />

      {/* Export Dialog */}
      <ExportDialog
        isOpen={cargoExportDialog.isOpen}
        onClose={cargoExportDialog.closeExportDialog}
        data={cargoExportDialog.data}
        {...cargoExportDialog.config}
      />

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="cargos"
        title="Create Tasks for Selected Cargo"
      />

      {/* QR Code Dialog from Cargo Management Hook */}
      <QRCodeDialog
        open={cargoManagement.state.qrCodeDialog.open}
        cargo={cargoManagement.state.qrCodeDialog.cargo}
        onOpenChange={(open) => {
          if (!open) {
            cargoManagement.handleDialogClose("qr-code");
          }
        }}
      />
    </PageTransition>
  );
}

// Export the page with RBAC protection
export default withRBAC(
  BatchDetailPage,
  "batches", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view batch details.
    </p>
  </div> // Fallback content
);
