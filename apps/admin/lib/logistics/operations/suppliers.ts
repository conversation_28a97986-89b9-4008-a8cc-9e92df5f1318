import { BaseService } from "../base/service";
import { ServiceResponse, ServiceListResponse, QueryParams } from "../types";
import { notificationService } from "../system/notifications";

// Supplier interfaces based on the actual database schema
// Table: suppliers (tracking_number, status, location, phone)
export interface Supplier {
  id: string;
  tracking_number?: string;
  phone?: string;
  location?: string;
  status:
    | "CREATED"
    | "ACTIVE"
    | "INACTIVE"
    | "PENDING"
    | "COMPLETED"
    | "CANCELLED";
  created_at: string;
  updated_at: string;
}

export interface SupplierInsert {
  tracking_number?: string;
  phone?: string;
  location?: string;
  status?:
    | "CREATED"
    | "ACTIVE"
    | "INACTIVE"
    | "PENDING"
    | "COMPLETED"
    | "CANCELLED";
}

export interface SupplierUpdate {
  tracking_number?: string;
  phone?: string;
  location?: string;
  status?:
    | "CREATED"
    | "ACTIVE"
    | "INACTIVE"
    | "PENDING"
    | "COMPLETED"
    | "CANCELLED";
}

export class SupplierService extends BaseService<
  Supplier,
  SupplierInsert,
  SupplierUpdate
> {
  protected tableName = "suppliers";

  /**
   * Get all active suppliers
   */
  async getActiveSuppliers(
    params: QueryParams = {}
  ): Promise<ServiceListResponse<Supplier>> {
    try {
      const result = await this.getAll({
        ...params,
        filters: {
          ...params.filters,
          status: "ACTIVE",
        },
      });

      return result;
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to fetch active suppliers",
        success: false,
      };
    }
  }

  /**
   * Get suppliers by status
   */
  async getSuppliersByStatus(
    status: string,
    params: QueryParams = {}
  ): Promise<ServiceListResponse<Supplier>> {
    try {
      const result = await this.getAll({
        ...params,
        filters: {
          ...params.filters,
          status,
        },
      });

      return result;
    } catch (error: any) {
      return {
        data: [],
        total: 0,
        error: error.message || "Failed to fetch suppliers by status",
        success: false,
      };
    }
  }

  /**
   * Search suppliers by tracking_number, phone, or location
   */
  async searchSuppliers(
    searchTerm: string,
    params: QueryParams = {}
  ): Promise<ServiceListResponse<Supplier>> {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select("*")
        .neq("status", "INACTIVE");

      // Add search conditions for available fields only
      if (searchTerm) {
        query = query.or(
          `tracking_number.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%`
        );
      }

      // Apply additional filters
      if (params.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
              query = query.in(key, value);
            } else {
              query = query.eq(key, value);
            }
          }
        });
      }

      // Apply sorting
      if (params.column && params.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params.limit) {
        query = query.limit(params.limit);
      }
      if (params.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 10) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          total: 0,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        total: count || data?.length || 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: [],
        total: 0,
        error: error.message || "Failed to search suppliers",
        success: false,
      };
    }
  }

  /**
   * Create a new supplier with notification
   */
  async createSupplier(
    data: SupplierInsert
  ): Promise<ServiceResponse<Supplier>> {
    try {
      const result = await this.create(data);

      if (result.success && result.data) {
        // Send notification
        await notificationService.create({
          title: "New Supplier Created",
          message: `Supplier with tracking number "${data.tracking_number || "N/A"}" has been created successfully.`,
          type: "success",
          entity_type: "suppliers",
          entity_id: result.data.id,
        });
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create supplier",
        success: false,
      };
    }
  }

  /**
   * Update supplier with notification
   */
  async updateSupplier(
    id: string,
    data: SupplierUpdate
  ): Promise<ServiceResponse<Supplier>> {
    try {
      const result = await this.update(id, data);

      if (result.success && result.data) {
        // Send notification
        await notificationService.create({
          title: "Supplier Updated",
          message: `Supplier with tracking number "${data.tracking_number || "Unknown"}" has been updated successfully.`,
          type: "info",
          entity_type: "suppliers",
          entity_id: result.data.id,
        });
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update supplier",
        success: false,
      };
    }
  }

  /**
   * Delete supplier (soft delete by setting status to INACTIVE)
   */
  async deleteSupplier(id: string): Promise<ServiceResponse<boolean>> {
    try {
      const result = await this.update(id, { status: "INACTIVE" });

      if (result.success) {
        // Send notification
        await notificationService.create({
          title: "Supplier Deleted",
          message: `Supplier has been deleted successfully.`,
          type: "warning",
          to: "*",
          entity_type: "suppliers",
          entity_id: id,
        });

        return {
          data: true,
          error: null,
          success: true,
        };
      }

      return {
        data: false,
        error: result.error,
        success: false,
      };
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to delete supplier",
        success: false,
      };
    }
  }

  /**
   * Get supplier statistics
   */
  async getSupplierStats(): Promise<
    ServiceResponse<{
      total: number;
      active: number;
      inactive: number;
      pending: number;
      recentlyAdded: number;
    }>
  > {
    try {
      const [totalResult, activeResult, inactiveResult, pendingResult] =
        await Promise.all([
          this.getAll({
            filters: { status: ["ACTIVE", "PENDING", "CREATED"] },
          }),
          this.getAll({ filters: { status: "ACTIVE" } }),
          this.getAll({ filters: { status: "INACTIVE" } }),
          this.getAll({ filters: { status: "PENDING" } }),
        ]);

      // Get recently added (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentResult = await this.supabase
        .from(this.tableName)
        .select("id")
        .gte("created_at", thirtyDaysAgo.toISOString())
        .neq("status", "INACTIVE");

      return {
        data: {
          total: totalResult.total || 0,
          active: activeResult.total || 0,
          inactive: inactiveResult.total || 0,
          pending: pendingResult.total || 0,
          recentlyAdded: recentResult.data?.length || 0,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: {
          total: 0,
          active: 0,
          inactive: 0,
          pending: 0,
          recentlyAdded: 0,
        },
        error: error.message || "Failed to get supplier statistics",
        success: false,
      };
    }
  }
}

// Export service instance
export const supplierService = new SupplierService();
